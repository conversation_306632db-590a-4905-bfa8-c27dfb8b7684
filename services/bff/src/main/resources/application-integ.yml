logging:
  level:
    root: INFO
    org.springframework: WARN
    org.springframework.security: INFO
    com.doorbit: TRACE

spring:
  cloud:
    gcp:
      credentials:
        encoded-key: ${GCP_SERVICE_ACCOUNT_IMAGE_UPLOADER}
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://auth-integ.doorbit.com/login/realms/doorbit
          jwk-set-uri: https://auth-integ.doorbit.com/login/realms/doorbit/protocol/openid-connect/certs
  graphql:
    schema:
      locations: "file:/schema"

management:
  prometheus:
    metrics:
      export:
        enabled: true

  logging:
    metrics:
      export:
        enabled: false

server:
  port: 8080

application:
  mongodb:
    base-uri: mongodb+srv://${MONGO_USER}:${MONGO_PW}@integ.6pkzkll.mongodb.net/?retryWrites=true&w=majority
  description-generation:
    open-ai:
      enabled: false # to save costs
      simulate-latency: 1000 # ms
  user-images:
    upload:
      gcp-bucket-name: "img-integ.doorbit.com"
  flow-configs:
    upload:
      gcp-bucket-name: "flow-configs-integ.doorbit.com"
  services:
    admin-boundary:
      host: http://geo-microservice-service.geo-microservice/geo
    billing:
      host: https://billing-microservice-kg6imd6m3a-ew.a.run.app/billing
    geolocalization:
      host: http://geo-microservice-service.geo-microservice/geo
    listing:
      host: http://bff-microservice-service.bff-microservice
      buildingTokenSecret: af47e77f-7179-4142-b497-2f77f602fd51 #ACHTUNG: Dieser Wert muss dem des Frontends entsprechen!
  geocoding:
    apple-maps:
      auth-token: ${APPLE_MAPS_AUTH_TOKEN}
  backend-uptime-checks: >
    http://geo-microservice-service.geo-microservice/actuator/health,
    http://keycloak.keycloak/login/realms/master