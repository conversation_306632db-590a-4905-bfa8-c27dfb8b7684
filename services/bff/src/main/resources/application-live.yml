logging:
  level:
    root: INFO
    org.springframework: WARN
    org.springframework.security: INFO
    com.doorbit: TRACE

spring:
  cloud:
    gcp:
      credentials:
        encoded-key: ${GCP_SERVICE_ACCOUNT_IMAGE_UPLOADER}
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: https://auth.doorbit.com/login/realms/doorbit/protocol/openid-connect/certs
  graphql:
    schema:
      locations: "file:/schema"

management:
  prometheus:
    metrics:
      export:
        enabled: true

server:
  port: 8080

application:
  mongodb:
    base-uri: mongodb+srv://${MONGO_USER}:${MONGO_PW}@live.zi8yk.mongodb.net/listing?retryWrites=true&w=majority
  user-images:
    upload:
      gcp-bucket-name: "img.doorbit.com"
  flow-configs:
    upload:
      gcp-bucket-name: "flow-configs.doorbit.com"
  services:
    admin-boundary:
      host: http://geo-microservice-service.geo-microservice/geo
    billing:
      host: https://billing-microservice-aakrrlprtq-ew.a.run.app/billing
    geolocalization:
      host: http://geo-microservice-service.geo-microservice/geo
    listing:
      host: http://bff-microservice-service.bff-microservice
      buildingTokenSecret: 16a4c960-355e-4416-8ead-2a687b1c31c8 #ACHTUNG: Dieser Wert muss dem des Frontends entsprechen!
  geocoding:
    apple-maps:
      auth-token: ${APPLE_MAPS_AUTH_TOKEN}

  backend-uptime-checks: >
    http://geo-microservice-service.geo-microservice/actuator/health,
    http://keycloak.keycloak/login/realms/master
