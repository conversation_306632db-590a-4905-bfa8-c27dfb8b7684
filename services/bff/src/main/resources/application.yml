server:
  tomcat:
    threads:
      max: 750
  port: 8083

logging:
  level:
    root: INFO
    org.springframework: INFO
    org.springframework.security: INFO
    com.doorbit: TRACE

spring:
  codec:
    max-in-memory-size: 512MB # wichtig für graphQL und große requests (z. B. file Uploads)
  cache:
    type: caffeine

  graphql:
    path: /graphql
    schema:
      locations: "file:${graphql_schema_location}"
    websocket:
      path: /subscriptions
      connection-init-timeout: 60s
    graphiql:
      path: /graphiql
      enabled: false
    tools:
      introspection-enabled: false

management:
  health:
    redis:
      enabled: false # if redis is down we don't want to be down
    diskSpace:
      enabled: false
  codec:
    max-in-memory-size: 10MB # wichtig für graphQL und große requests
  endpoints:
    enabled-by-default: false
    web.exposure.include: "*"
  endpoint:
    health:
      enabled: true
      show-details: always
    prometheus:
      enabled: true
  metrics:
    # Disables most OOTB micrometer metrics. All this stuff is already provided by GCP and GKE.
    # Metrics ingestion into GCP Stackdriver is relatively expensive, so we want to keep the number of metrics low.
    enable:
      graphql: true
      http: true
      # These are disabled because they don't add value and lead to a lot of noise in prometheus (and costs)
      graphql.datafetcher.active: false
      graphql.request.active: false
      http.server.requests.active: false
      jvm: false
      tomcat: false
      system: false
      executor: false
      logback: false
      disk: false
      application: false
      process: false
      spring: false

  prometheus:
    metrics:
      export:
        enabled: false

  logging:
    metrics:
      export:
        enabled: false
        # Extending the step from 1m to 5m would save us 80% of the metrics and thus 80% of the cost
        step: 1m

application:
  mongodb:
    base-uri: **************************************

  # We ain't using the caffeine default implementation using caffeine.spec config property, because it cannot configure different TTLs for
  # different caches, etc. However we still configure the cache type to caffeine using spring.cache.type: caffeine.
  # Hence, we're creating caches on our own using the CacheConfig.kt class.
  # When requiring a new cache, just add it to the cacheNames list here and configure the cache accordingly.
  # You are then ready to go to use the cache by defining @Cacheable("your-cache-name") on the method you want to cache.
  #
  # MAX-SIZE CURRENTLY NOT SUPPORTED BY HAZELCAST REPLICATED MAPS
  #
  #
  cache:
    default:
      ttl-in-minutes: 1440
    flowConfig:
      ttl-in-minutes: 5
    groups:
      ttl-in-minutes: 5
    groupsOfUser:
      ttl-in-minutes: 5
    groupMembers:
      ttl-in-minutes: 5
    keycloakUsers:
      ttl-in-minutes: 5
    listingSharingSettings:
      ttl-in-minutes: 5
    isAllowedToEditListing:
      ttl-in-minutes: 5
    buildingScans:
      ttl-in-minutes: 60
  mailingservice:
    api-key: ${MAILING_SERVICE_API_KEY:*********************************************************************}
    feedback-email: ${FEEDBACK_EMAIL:<EMAIL>}
    support-email: ${SUPPORT_EMAIL:<EMAIL>}
    contact-form-email: ${CONTACT_FORM_EMAIL:<EMAIL>}
  user-uploads:
    cache-control: "public, max-age: 86400"
    images:
      batch-processing-concurrency: 2
      compression: 0.90
      size-boundaries: # Dies sind keine fixen Auflösungen sondern jeweils Maximalbreiten und Höhen. Der Algorithmus betrachtet automatisch das Seitenverhältnis.
        original: 1920 # Basisauflösung von der aus wir zu allen anderen resamplen
        thumbnail: 500
        mini: 16
  description-generation:
    open-ai:
      enabled: true
      token: ***************************************************
      timeout: 70s # ms or s
  fields:
    # In case an obfuscation for the shown location is desired by the listing owner,
    # these are the configuration properties to control the actual roughening of the obfuscated location.
    location-obfuscation:
      min-distance: 75 # min distance a random coordinate should be created from the real location, in meters
      max-distance: 250 # max distance, respectively, in meters
  enrichment:
    distances:
      # Obfuscation is used to prevent reverse engineering of the exact location of a listing by POI analysis.
      # It is only applied if the listing owner has decided to obfuscate the location.
      # The obfuscation works on the basis of the real location of the listing and the distance to the POIs.
      # The obfuscated coordinates are not used for this.
      obfuscation:
        # POIs having a distance lower than this value will be fixed to the specified minimum distance.
        show-distance-not-lower-than: 50 # meters
        # POIs' distances will be obfuscated by a random value specified by this value.
        # The value is calculated as a random between -value and +value and is added to the real distance.
        # Note that if this radius is too small, the obfuscation will not be very effective as an exploiter can strip down the approximate location quite closely.
        # A value of 75m is a good tradeoff between obfuscation and usability.
        show-fuzzy-radius: 75 # meters
  services:
    keycloak:
      host: http://keycloak.keycloak/login
      client-id: ${KEYCLOAK_CLIENT_ID}
      client-secret: ${KEYCLOAK_CLIENT_SECRET}
    admin-boundary:
      host: http://localhost:8080/geo
    billing:
      host: http://localhost:8088/billing
    geolocalization:
      host: http://localhost:8080/geo
    listing:
      host: http://localhost:8083/listing
  geocoding:
    apple-maps:
      auth-token: "apple maps is not supported in local dev as we have only 1 api key for all environments"

  backend-uptime-checks: >
    http://localhost:8081/actuator/health,
    http://localhost:8082/actuator/health