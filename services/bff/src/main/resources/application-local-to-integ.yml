logging:
  level:
    com:
      doorbit: TRACE
    org:
      springframework: WARN

spring:
  cloud:
    gcp:
      storage:
        enabled: false
      core:
        enabled: false
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://auth-integ.doorbit.com/login/realms/doorbit
          jwk-set-uri: https://auth-integ.doorbit.com/login/realms/doorbit/protocol/openid-connect/certs
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
  jpa:
    show-sql: false
    properties:
      hibernate.format_sql: false
  graphql:
    graphiql:
      enabled: true
    cors:
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"
    tools:
      introspection-enabled: true

application:
  mongodb:
    base-uri: mongodb+srv://${MONGO_USER}:${MONGO_PW}@integ.6pkzkll.mongodb.net/?retryWrites=true&w=majority
  cloudflare-cookie: "CF_Authorization=eyJhbGciOiJSUzI1NiIsImtpZCI6IjkyYTk3NGE3YmQyMjgyMDQ5OTA1M2YzNjYyODlhZDIzMzEyNGFhMTUzODMyM2ExMDJhZWNjMDcwMDg3YThmN2QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.a9rwmCDgmCpu0TVKxujkbrVjHy19BS0G-l47p_DNhWHpyObsMfj0uGZUiJRLRyHJvp0FTDcwmvgv0Km7fZPglWVnqlVUcLlzvoKHeS541XyhDrGb1yEUm3M242-Nx1zA1Aa9td3p5xzOt1c3NRxkQ3NwPPKQXUp7KEEkPXKsUUbk299y9VkwtGizLqa1hkNcUW-Ys6QMaGpBruBkzJXSPpZ6zsflybdqSkVxheJxujYzbzWnMJR8vGHfH7FpaI7EFhM2_1o4G_ebk5OmDWB74WvotPVCFMP6GXXpEMxYa-Akd2F4-fSMMXZxvrR-FtbVx8KsC0-v1CuajDTR1hJ0Ng"
  services:
    keycloak:
      host: https://auth-integ.doorbit.com/login
    admin-boundary:
      host: https://integ.doorbit.com/geo
    geolocalization:
      host: https://integ.doorbit.com/geo
    listing:
      host: http://localhost:8083
      buildingTokenSecret: 0be861e6-a0b6-421e-965b-4db73f9023d0 #ACHTUNG: Dieser Wert muss dem des Frontends entsprechen!
  geocoding:
    apple-maps:
      auth-token: eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IkM4Mjg1WlI4RDcifQ.************************************************************************.mXmt7USs60qHfSXz56FD5Q70R_jPpHNTn33x7nrFQw7P2ClUTDDhV1NCKKnyjVYT26aLnyS9kJxExTRz0UnL0g

management:
  endpoint:
    health:
      show-details: always
    features:
      enabled: true
    configprops:
      enabled: true
    httpexchanges:
      enabled: true
    info:
      enabled: true
  endpoints:
    enabled-by-default: true
