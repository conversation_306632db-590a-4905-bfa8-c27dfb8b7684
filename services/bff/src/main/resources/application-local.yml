logging:
  level:
    root: INFO
    org.springframework: INFO
    com.doorbit: TRACE

spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: https://auth-integ.doorbit.com/login/realms/doorbit/protocol/openid-connect/certs
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
  jpa:
    show-sql: false
    properties:
      hibernate.format_sql: false
  cloud:
    gcp:
      storage:
        enabled: false
      core:
        enabled: false
  graphql:
    graphiql:
      enabled: true
    cors:
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"
    tools:
      introspection-enabled: true

management:
  prometheus:
    metrics:
      export:
        enabled: false

  logging:
    metrics:
      export:
        # Enabling this toggle will log metrics to the console every 15s (hardcoded in MicrometerConfig)
        enabled: false

application:
  mongodb:
    base-uri: **************************************
  cache:
    default:
      ttl-in-minutes: 2
    flowConfig:
      ttl-in-minutes: 1
  services:
    listing:
      buildingTokenSecret: 0be861e6-a0b6-421e-965b-4db73f9023d0