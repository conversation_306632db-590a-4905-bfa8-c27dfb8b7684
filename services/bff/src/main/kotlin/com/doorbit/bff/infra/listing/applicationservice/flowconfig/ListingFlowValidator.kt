package com.doorbit.bff.infra.listing.applicationservice.flowconfig

import com.doorbit.bff.infra.listing.applicationservice.flowconfig.completion.FlowConfigElementCompletionMapper.completeBooleanElement
import com.doorbit.bff.infra.listing.applicationservice.flowconfig.completion.FlowConfigElementCompletionMapper.completeNumberElement
import com.doorbit.bff.infra.listing.applicationservice.flowconfig.completion.FlowConfigElementCompletionMapper.completeStringUiElement
import com.doorbit.bff.infra.listing.applicationservice.flowconfig.completion.FlowConfigElementCompletionMapper.postProcessViewElement
import com.doorbit.bff.infra.listing.applicationservice.flowconfig.completion.FlowConfigImageDownloadService
import com.doorbit.bff.infra.listing.api.dto.ArrayUIElementDto
import com.doorbit.bff.infra.listing.api.dto.BooleanUIElementDto
import com.doorbit.bff.infra.listing.api.dto.ChipGroupUIElementDto
import com.doorbit.bff.infra.listing.api.dto.ContactUIElementDto
import com.doorbit.bff.infra.listing.api.dto.CustomUIElementDto
import com.doorbit.bff.infra.listing.api.dto.DateUIElementDto
import com.doorbit.bff.infra.listing.api.dto.FieldTextUIElementDto
import com.doorbit.bff.infra.listing.api.dto.FileUIElementDto
import com.doorbit.bff.infra.listing.api.dto.GroupUIElementDto
import com.doorbit.bff.infra.listing.api.dto.ImageGalleryUIElementDto
import com.doorbit.bff.infra.listing.api.dto.KeyValueListUIElementDto
import com.doorbit.bff.infra.listing.api.dto.ListingFlowDto
import com.doorbit.bff.infra.listing.api.dto.NumberUIElementDto
import com.doorbit.bff.infra.listing.api.dto.PatternLibraryElementDto
import com.doorbit.bff.infra.listing.api.dto.SingleSelectionUIElementDto
import com.doorbit.bff.infra.listing.api.dto.StringUIElementDto
import com.doorbit.bff.infra.listing.api.dto.TableUIElementDto
import com.doorbit.bff.infra.listing.api.dto.TextUIElementDto
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException

object ListingFlowValidator {

    fun withResolvedFieldDefinitions(flowDto: ListingFlowDto): ListingFlowDto {
        flowDto.pagesEdit.forEach { page ->
            page.elements.forEach { postProcessEditComponents(it, flowDto, 0) }
        }

        flowDto.pagesView.forEach { page ->
            page.elements.forEach { postProcessViewComponents(it, flowDto, 0) }
        }

        return flowDto
    }

    private fun postProcessViewComponents(pattern: PatternLibraryElementDto, flowDto: ListingFlowDto, treeLevel: Int) {
        when (val element = pattern.element) {
            is StringUIElementDto -> completeStringUiElement(element, treeLevel)
            is NumberUIElementDto -> completeNumberElement(element, treeLevel)

            is CustomUIElementDto -> {
                element.treeLevel = treeLevel
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.id = if (element.id == null) element.hashCode().toString() else element.id
                element.elements?.forEach { postProcessViewComponents(it, flowDto, treeLevel + 1) }
                element.subFlows?.forEach { it.elements.forEach { subflow -> postProcessViewComponents(subflow, flowDto, 0) } }
            }

            is TableUIElementDto -> {
                element.treeLevel = treeLevel
                element.hideIndexColumn = if (element.hideIndexColumn == null) false else element.hideIndexColumn
                element.columns.forEach { column -> column.fieldValue?.let { postProcessViewElement(it, flowDto) } }
            }

            is KeyValueListUIElementDto -> {
                element.treeLevel = treeLevel
                element.items.forEach { item -> item.fieldValue?.let { postProcessViewElement(it, flowDto) } }
            }

            is ChipGroupUIElementDto -> {
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.treeLevel = treeLevel
            }

            is ImageGalleryUIElementDto -> {
                element.treeLevel = treeLevel
            }

            is GroupUIElementDto -> {
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.treeLevel = treeLevel
                element.elements.forEach { postProcessViewComponents(it, flowDto, treeLevel + 1) }
            }

            is TextUIElementDto -> {
                element.type = if (element.type == null) "PARAGRAPH" else element.type
                element.treeLevel = treeLevel
            }

            is FieldTextUIElementDto -> {
                element.treeLevel = treeLevel
                element.fieldValue?.let { postProcessViewElement(it, flowDto) }
                element.align = if (element.align == null) "CENTER" else element.align
                element.type = if (element.type == null) "PARAGRAPH" else element.type
            }

            is ContactUIElementDto -> {
                element.treeLevel = treeLevel
            }

            is DateUIElementDto -> {
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.treeLevel = treeLevel
            }

            is FileUIElementDto -> {
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.treeLevel = treeLevel
            }

            is SingleSelectionUIElementDto -> {
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.options.forEach {
                    if (it.exampleImage != null)
                        it.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(it.exampleImage!!)
                }

                element.treeLevel = treeLevel
                element.otherUserValue?.let { completeStringUiElement(it.textUiElement, treeLevel) }
            }

            else -> throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Unknown pattern type: ${pattern.element.patternType}")
        }
    }

    private fun postProcessEditComponents(pattern: @Valid PatternLibraryElementDto, flowDto: ListingFlowDto, treeLevel: Int) {
        when (val element = pattern.element) {
            is BooleanUIElementDto -> completeBooleanElement(element, treeLevel)
            is StringUIElementDto -> completeStringUiElement(element, treeLevel)
            is NumberUIElementDto -> completeNumberElement(element, treeLevel)
            is CustomUIElementDto -> {
                element.treeLevel = treeLevel
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.id = if (element.id == null) element.hashCode().toString() else element.id
                element.elements?.forEach { postProcessEditComponents(it, flowDto, treeLevel + 1) }
                element.subFlows?.forEach { subflow -> subflow.elements.forEach { postProcessEditComponents(it, flowDto, 0) } }
            }

            is ArrayUIElementDto -> {
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.treeLevel = treeLevel
                element.elements.forEach { postProcessEditComponents(it, flowDto, treeLevel + 1) }
            }

            is GroupUIElementDto -> {
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.treeLevel = treeLevel
                element.elements.forEach { postProcessEditComponents(it, flowDto, treeLevel + 1) }
            }

            is TextUIElementDto -> {
                element.treeLevel = treeLevel
            }

            is SingleSelectionUIElementDto -> {
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.options.forEach {
                    if (it.exampleImage != null)
                        it.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(it.exampleImage!!)
                }

                element.treeLevel = treeLevel
                element.otherUserValue?.let { completeStringUiElement(it.textUiElement, treeLevel) }
            }

            is ChipGroupUIElementDto -> {
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.treeLevel = treeLevel
                element.chips.forEach { completeBooleanElement(it, treeLevel) }
            }

            is KeyValueListUIElementDto -> {
                element.treeLevel = treeLevel
                element.items.forEach { item -> item.fieldValue?.let { postProcessViewElement(it, flowDto) } }
            }

            is DateUIElementDto -> {
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.treeLevel = treeLevel
            }

            is FileUIElementDto -> {
                if (element.exampleImage != null)
                    element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

                element.treeLevel = treeLevel
            }

            is ContactUIElementDto -> {
                element.treeLevel = treeLevel
            }

            else -> throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Unknown pattern type: ${pattern.element.patternType}")
        }
    }

}