package com.doorbit.bff.infra.listing.extensions

import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec
import com.doorbit.bff.infra.listing.model.listing.fields.validation.FieldValidationHint
import com.doorbit.bff.infra.listing.model.listing.fields.validation.FieldValidationItem

/**
 * Extension function for validating a field value of the Listing FieldModel.
 * Returns a validation item if the field is invalid, otherwise null.
 *
 * @see ListingFields
 */
fun Any?.validate(fieldSpec: FieldSpec<*>, required: Boolean): FieldValidationItem? {

    if (required && this == null) {
        return FieldValidationItem(hint = FieldValidationHint.MUST_NOT_BE_NULL, fieldSpec = fieldSpec)
    }

    // Optional or non-null value
    return if (this == null) null else {
        val validationSpec = fieldSpec.validationSpec
        val validationResult = fieldSpec.fieldValidator.validate(validationSpec, this)
        validationResult?.withFieldSpec(fieldSpec)
    }
}