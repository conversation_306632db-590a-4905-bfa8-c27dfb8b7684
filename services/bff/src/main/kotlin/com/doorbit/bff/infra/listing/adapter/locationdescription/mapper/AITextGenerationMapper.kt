package com.doorbit.bff.infra.listing.adapter.locationdescription.mapper

import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldName
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec.*
import com.doorbit.bff.infra.listing.model.listing.fields.SubType
import com.doorbit.bff.infra.listing.model.listingenrichment.*
import kotlin.math.ceil

private val excludedFields = listOf(
    AddressStreetField.fieldName,
    AddressHouseNumberField.fieldName,
    AddressLatitudeField.fieldName,
    AddressLongitudeField.fieldName,
    ListingRegionField.fieldName,
    LocationDescriptionField.fieldName,
    TitleField.fieldName,
)

object AITextGenerationMapper {

    fun maptoAiTextGeneratorListingDescriptionInput(data: Map<FieldName, Any?>): Map<FieldName, Any?> {
        return data.filter {
            if (it.value is Boolean) {
                it.value == true && !excludedFields.contains(it.key)
            } else !excludedFields.contains(it.key)
        }.map {
            if (it.key == SubTypeField.fieldName && it.value != null) {
                it.key to (SubType.valueOf(it.value.toString())).gptTranslationDe
            } else {
                it.key to it.value
            }
        }.toMap()
    }

    fun mapToAITextGeneratorLocationInput(geoLocalization: GeoLocalization): AiTextGeneratorLocationInput {

        val attractiveness = geoLocalization.geoAttractiveness
        return AiTextGeneratorLocationInput(
            achievableScore = attractiveness.achievableScore,
            score = attractiveness.score,
            categories = attractiveness.categories.map { mapToAiTextGeneratorLocationInputGeoAttractivenessCategory(it, geoLocalization.poiGroups) }
        )

    }

    private fun mapToAiTextGeneratorLocationInputGeoAttractivenessCategory(cat: GeoAttractivenessCategory, poiGroups: List<POIGroup>): AiTextGeneratorLocationInputGeoAttractivenessCategory {
        return AiTextGeneratorLocationInputGeoAttractivenessCategory(
            categoryKey = cat.categoryKey,
            achievableScore = cat.achievableScore,
            score = cat.score,
            ratingCriteria = cat.ratingCriteria.map { mapToAiTextGeneratorLocationInputGeoAttractivenessRatingCriteria(it, poiGroups) }
        )
    }

    private fun mapToAiTextGeneratorLocationInputGeoAttractivenessRatingCriteria(ratingCriteria: GeoAttractivenessRatingCriteria, poiGroups: List<POIGroup>): AiTextGeneratorLocationInputGeoAttractivenessRatingCriteria {
        return AiTextGeneratorLocationInputGeoAttractivenessRatingCriteria(
            key = ratingCriteria.key,
            achievableScore = ratingCriteria.achievableScore,
            score = ratingCriteria.score,
            pointsOfInterest = ratingCriteria.pointsOfInterest?.map { mapToAiTextGeneratorLocationInputGeoAttractivenessPoi(it, poiGroups) }
        )
    }

    private fun mapToAiTextGeneratorLocationInputGeoAttractivenessPoi(geoAttractivenessPoi: GeoAttractivenessPoi, poiGroups: List<POIGroup>): AiTextGeneratorLocationInputGeoAttractivenessPoi {

        val poiType = geoAttractivenessPoi.poiType

        return AiTextGeneratorLocationInputGeoAttractivenessPoi(
            poiType = poiType,
            pois = if (geoAttractivenessPoi.available) getPoisForType(poiType, poiGroups) else emptyList()
        )
    }

    private fun getPoisForType(poiType: PoiType, poiGroups: List<POIGroup>): List<AiTextGeneratorLocationInputGeoAttractivenessPoiListItem> {

        val allPoisOfType = poiGroups.firstOrNull { it.poiType == poiType } ?: return emptyList()

        return allPoisOfType.poiList.take(3).map {
            AiTextGeneratorLocationInputGeoAttractivenessPoiListItem(
                poiName = it.name,
                available = true,
                distanceInMeters = it.distanceInMeters,
                travelTimeByFeet = postProcessTravelTime(TravelDurationService.calculateTravelDurationByFeet(it.distanceInMeters).seconds, feet = true),
                travelTimeByBike = postProcessTravelTime(TravelDurationService.calculateTravelDurationByBike(it.distanceInMeters).seconds, bike = true),
                travelTimeByCar = postProcessTravelTime(TravelDurationService.calculateTravelDurationByCar(it.distanceInMeters).seconds),
            )
        }
    }

    private fun postProcessTravelTime(seconds: Long, feet: Boolean = false, bike: Boolean = false): Int? {
        val minutes = secondsToMinutes(seconds)
        // dont show long walking or biking distances
        return if ((feet || bike) && minutes > 15) null else minutes
    }

    fun secondsToMinutes(seconds: Long): Int {
        return ceil(seconds / 60.0).toInt()
    }

}