package com.doorbit.bff.infra.listing.applicationservice.flowconfig

import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.infra.listing.api.dto.*
import com.doorbit.bff.infra.listing.model.listing.ListingField
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldName

class ListingFlowConfigUtil(val config: ListingFlowDto) {

    fun invisibleFields(fields: Collection<ListingField>): List<FieldName> {
        return InvisibleFieldsFinder.invisibleFields(config, fields)
    }

    companion object : WithLogger() {

        fun extractFieldNames(patternElement: PatternLibraryElementDto): List<FieldName> {
            return when (val pattern = patternElement.element) {
                is BooleanUIElementDto -> listOf(pattern.fieldId.fieldName)
                is NumberUIElementDto -> listOf(pattern.fieldId.fieldName)
                is StringUIElementDto -> listOf(pattern.fieldId.fieldName)
                is DateUIElementDto -> listOf(pattern.fieldId.fieldName)
                is ArrayUIElementDto -> pattern.elements.flatMap(::extractFieldNames)
                is CustomUIElementDto -> pattern.elements?.flatMap(::extractFieldNames) ?: emptyList()
                is GroupUIElementDto -> pattern.elements.flatMap(::extractFieldNames)
                is SingleSelectionUIElementDto -> listOf(pattern.fieldId.fieldName)
                is ChipGroupUIElementDto -> pattern.chips.flatMap { chip -> listOf(chip.fieldId.fieldName) }
                is FileUIElementDto -> listOf(pattern.idFieldId.fieldName)
                else -> emptyList()
            }
        }

    }

}
