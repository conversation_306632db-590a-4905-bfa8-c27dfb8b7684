package com.doorbit.bff.infra.listing.adapter.geolocalization

import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.e
import com.doorbit.bff.core.domain.extension.t
import com.doorbit.bff.infra.adapter.api.geo.AdminBoundaryApi
import com.doorbit.bff.infra.adapter.api.geo.GeolocalizationApi
import com.doorbit.bff.infra.adapter.api.geo.dto.AdminBoundaryDto
import com.doorbit.bff.infra.listing.adapter.geolocalization.mapper.GeoLocalizationMapper
import com.doorbit.bff.infra.listing.model.listing.GeoLocalizationAdapter
import com.doorbit.bff.infra.listing.model.listing.Listing
import com.doorbit.bff.infra.listing.model.listingenrichment.AdminBoundaryIds
import com.doorbit.bff.infra.listing.model.listingenrichment.GeoLocalization
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class GeoLocalizationGeoApiAdapter(
    private val geoLocalizationApi: GeolocalizationApi,
    private val adminBoundaryApi: AdminBoundaryApi,
    @Value(value = "\${application.enrichment.distances.obfuscation.show-distance-not-lower-than}") val distanceNotLowerThan: Int,
    @Value(value = "\${application.enrichment.distances.obfuscation.show-fuzzy-radius}") val fuzzyRadius: Int,

) : GeoLocalizationAdapter {

    private companion object : WithLogger()

    override fun fetchGeoLocalizationFor(listing: Listing): GeoLocalization? {
        val latLng = listing.getLocationForEnrichment() ?: return null

        val start = System.currentTimeMillis()

        try {
            val response = geoLocalizationApi.geoLocalize(latLng.first, latLng.second, distanceNotLowerThan, fuzzyRadius)
            LOGGER.t { "GeoService answered in " + (System.currentTimeMillis() - start) + "ms." }

            return GeoLocalizationMapper.fromDto(response)
        } catch (e: Exception) {
            LOGGER.e { "Something went wrong when calling GeoService to geoLocalize listing ${listing.id}. $e" }
            throw RuntimeException("Couldn't process response of GeoService for Property GeoLocalization. See logs for further details.")
        }
    }

    fun findAdminBoundaries(boundaries: AdminBoundaryIds?): List<AdminBoundaryDto> {
        if (boundaries == null) {
            return emptyList()
        }

        val boundaryIds = mutableListOf<String>()

        boundaries.stateBoundaryId?.let { boundaryIds.add(it) }
        boundaries.countyBoundaryId?.let { boundaryIds.add(it) }
        boundaries.cityBoundaryId?.let { boundaryIds.add(it) }
        boundaries.districtBoundaryId?.let { boundaryIds.add(it) }

        if (boundaryIds.isEmpty()) {
            return emptyList()
        }

        val start = System.currentTimeMillis()

        try {
            val adminBoundaries = adminBoundaryApi.getAdminBoundaries(boundaryIds)

            LOGGER.t { "GeoService answered for AdminBoundaries in " + (System.currentTimeMillis() - start) + "ms." }

            return adminBoundaries
        } catch (e: Exception) {
            LOGGER.e {
                "Something went wrong when calling GeoService to fetch admin boundaries. $e"
            }
            return emptyList()
        }
    }

}
