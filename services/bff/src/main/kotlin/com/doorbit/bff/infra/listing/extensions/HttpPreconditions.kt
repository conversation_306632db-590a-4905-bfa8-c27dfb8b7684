package com.doorbit.bff.infra.listing.extensions

import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract

/**
 * Throws an [ResponseStatusException] of type [HttpStatus.NOT_FOUND] with the result of calling [lazyMessage] if the [value] is null.
 * Otherwise, returns the not null value.
 */
@OptIn(ExperimentalContracts::class)
inline fun <T : Any> notFoundOnNull(value: T?, lazyMessage: () -> Any): T {
    contract {
        returns() implies (value != null)
    }

    if (value == null) {
        val message = lazyMessage()
        throw ResponseStatusException(HttpStatus.NOT_FOUND, message.toString())
    } else {
        return value
    }
}
