package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.core.domain.applicationservice.userprofile.UserProfileService
import com.doorbit.bff.infra.adapter.googlechat.ChatAdapter
import com.doorbit.bff.infra.listing.extensions.toListingId
import com.doorbit.bff.infra.listing.model.listing.*
import com.doorbit.bff.infra.listing.model.sharedlisting.SharedListingSettingsRepository
import com.doorbit.bff.infra.listing.repo.ListingRepository
import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.i
import com.doorbit.bff.core.domain.extension.w
import org.springframework.stereotype.Service
import java.lang.Thread.startVirtualThread
import java.time.Duration
import java.time.Instant

@Service
class ListingWorkflowService(
    private val listingRepository: ListingRepository,
    private val listingEnrichmentService: ListingEnrichmentService,
    private val chatAdapter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    private val listingIdFactory: ListingIdFactory,
    private val sharedListingSettingsRepository: SharedListingSettingsRepository,
    private val locationObfuscationService: ListingLocationObfuscationService,
    private val listingImageStorageService: ListingImageStorageService,
    private val fieldsSetService: FieldsSetService,
    private val userProfileService: UserProfileService,
) {

    /**
     * Predecessor status: n/a
     *
     * First status of a listing is preview.
     * The listing has to be created before the "summary page" of the created listing can be shown.
     * The user can view the listing in this mode, check if all data is correct and then publish it.
     *
     * Successor status: REVIEW.
     */
    fun listingCreated(offlineListingId: String?, userId: UserId, flowId: String, userBillingId: GroupId, userGroupId: String?, isTestListing: Boolean, isDemoListing: Boolean): Listing =
        Listing(
            userId = userId,
            flowId = flowId,
            billingUserId = userBillingId,
            billingGroupId = userGroupId,
            id = offlineListingId?.toListingId() ?: listingIdFactory.newId(),
            testListing = isTestListing,
            demoListing = isDemoListing,
            // test listings are always unlocked
        ).also {
            listingRepository.save(it)

            startVirtualThread {
                val userProfile = userProfileService.getUserProfile(userId)
                if (!userProfile.email.contains("<EMAIL>")) {
                    chatAdapter.sendGenericMessage("Listing https://doorbit.com/listing/${it.id.id}/ wurde von ${userProfile.email} angelegt.")
                }
            }
            return it
        }

    fun listingDeleted(listingId: ListingId) {

        listingRepository.setStatusDeleted(listingId)
        listingRepository.setFinalDeleteAfter(listingId, Instant.now().plus(Duration.ofDays(30)))

        val listing = listingRepository.findById(listingId)
        listingEnrichmentService.deleteById(listingId)
            .also { deleteListingImages(listing) }
            .also { sharedListingSettingsRepository.deleteById(listingId) }
    }

    private fun deleteListingImages(listing: Listing) {
        startVirtualThread {
            if (listing.assets.images().isEmpty()) {
                return@startVirtualThread
            }

            listing.assets.images().parallelStream().forEach {
                try {
                    listingImageStorageService.deleteImages(listOf(it.original.path.filename, it.thumbnail.path.filename, it.mini.path.filename))
                } catch (e: Exception) {
                    LOGGER.w { "Could not delete image ${it.id} for listing ${listing.id}" }
                }
            }

            LOGGER.i { "Deleted all images for listing ${listing.id}" }
        }
    }

    /**
     * Predecessor status: any.
     *
     * In almost all status the user can update the listing. An update may have certain implications to the
     * review process. For example, if the listing is in status "PUBLISHED" and the user updates the listing,
     * the review process has to be restarted, since any review-critical data could have changed.
     *
     * Successor: unchanged or REVIEW.
     */
    fun listingUpdated(listingId: ListingId? = null, updateResult: ListingFieldUpdateResult, aListing: Listing? = null) {
        val listing = aListing ?: listingRepository.findById(listingId!!)

        if (updateResult.isListingLocationChanged()) {
            locationObfuscationService.updateLocationObfuscation(listing, updateResult.getObfuscatedLocationSetting())
            listingEnrichmentService.updateListingEnrichment(listing)
        }

        if (updateResult.getDeletedFields().isNotEmpty()) {
            handleDeletedFieldImages(listing, updateResult)
        }

        fieldsSetService.onFieldsSet(listing, updateResult)
    }

    private fun handleDeletedFieldImages(listing: Listing, updateResult: ListingFieldUpdateResult) {
        val deletedImages = updateResult.getDeletedFields().entries.filter { it.key.lowercase().contains("image_id") || it.key.lowercase().contains("pictures_id") }
        if (deletedImages.isEmpty()) {
            return
        }

        LOGGER.i { "Detected these images to delete: $deletedImages" }
        val imageIds = deletedImages.flatMap { field -> field.value.mapNotNull { it.value?.toString() } }
        imageIds.mapNotNull { listing.assets.imageById(it) }.parallelStream().forEach { image ->
            try {
                listingImageStorageService.deleteImages(listOf(image.original.path.filename, image.thumbnail.path.filename, image.mini.path.filename))
                listing.assets.deleteImage(image.id)
            } catch (e: Exception) {
                LOGGER.w { "Could not delete image ${image.id} for listing ${listing.id}. Maybe it was not updated yet. Continueing" }
            }
        }

        listingRepository.save(listing)
    }

    companion object : WithLogger()

}

