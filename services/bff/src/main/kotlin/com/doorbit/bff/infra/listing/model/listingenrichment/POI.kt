package com.doorbit.bff.infra.listing.model.listingenrichment

/**
 * Some POIs don't have a name, like coastlines for instance. GeoService is making sure that for a lot of POI Types, a name is mandatory and guaranteed to be present,
 * like Supermarkets, Restaurants, etc.
 */
data class POI(
    val name: String?,
    val distanceInMeters: Int,
    val latitude: Double,
    val longitude: Double,
    val details: Map<String, String>? = null
) {

    fun schoolTypeShouldContain(schoolType: String): Boolean {
        return details?.get("SCHOOL_TYPE")?.contains(schoolType, ignoreCase = true) ?: false
    }

}