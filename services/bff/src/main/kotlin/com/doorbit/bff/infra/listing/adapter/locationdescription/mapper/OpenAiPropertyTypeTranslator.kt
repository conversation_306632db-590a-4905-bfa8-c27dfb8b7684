package com.doorbit.bff.infra.listing.adapter.locationdescription.mapper

import com.doorbit.bff.infra.listing.model.listing.fields.SubType
import com.doorbit.bff.infra.listing.model.listing.fields.SubType.*

/**
 * Translates the listing type to a human readable text for Open AI.
 */
object OpenAiPropertyTypeTranslator {

    fun translatePropertyType(listingFields: SubType): String {
        return when (listingFields) {
            //TODO die übersetzungen sind nicht korrekt (oder die enum values)
            DETACHED -> "Einfamilienhaus"
            SEMI_DETACHED -> "Doppelhaushälfte"
            BUNGALOW -> "Bungalow"
            VILLA -> "Villa"
            CASTLE -> "Schloss"
            COUNTRY_HOUSE -> "Landhaus"
            FINCA -> "Finca"
            TOWNHOUSE -> "Reihenhaus"
            BELOW_GROUND_FLOOR -> "Souterrain Wohnung"
            LOFT -> "Loft"
            DUPLEX -> "Maisonette"
            PENTHOUSE -> "Penthouse"
            FLOOR -> "Etagenwohnung"
            RAISED_GROUND_FLOOR -> "Hochparterre Wohnung"
            GROUND_FLOOR -> "Erdgeschoss Wohnung"
//            PARKING_SPOT -> "Stellplatz/Garage"
//            UNDERGROUND_GARAGE -> "Tiefgarage"
//            SINGLE_GARAGE -> "Einzelgarage"
//            RETAIL -> "Einzelhandel"
//            STORE -> "Ladenfläche"
//            GASTRO -> "Gastronomie"
//            KIOSK -> "Kiosk"
//            OFFICE -> "Büro"
//            DOCTORS_OFFICE -> "Arztpraxis"
            else -> throw IllegalArgumentException("Unknown listing type: $listingFields")
        }
    }

}