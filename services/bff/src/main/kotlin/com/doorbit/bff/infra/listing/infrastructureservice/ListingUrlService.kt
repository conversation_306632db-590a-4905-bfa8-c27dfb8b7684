package com.doorbit.bff.infra.listing.infrastructureservice

import com.doorbit.bff.infra.listing.model.listing.ListingId

class ListingUrlService {
    companion object {
        fun createListingLdpUrl(environmentInfo: String, id: ListingId): String {
            return if (environmentInfo == "live") {
                "https://doorbit.com/listing/${id.id}/"
            } else {
                "https://integ.doorbit.com/listing/${id.id}/"
            }
        }
    }
}