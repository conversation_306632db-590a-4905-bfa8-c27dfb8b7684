package com.doorbit.bff.infra.extension

import org.locationtech.jts.geom.LinearRing

typealias LinearRingArray = Array<CoordinateArray>
typealias LinearRingList = List<CoordinateList>

fun LinearRing.toMultiDimensionalArray(): LinearRingArray {
    return Array(numPoints) { pointIndex ->
        val coordinate = getCoordinateN(pointIndex)
        coordinate.toDoubleArray()
    }
}

fun LinearRing.toMultiDimensionalList(): LinearRingList {
    return List(numPoints) { pointIndex ->
        val coordinate = getCoordinateN(pointIndex)
        coordinate.toDoubleList()
    }
}