package com.doorbit.bff.infra.listing.model.listing.building

import com.doorbit.bff.infra.listing.model.math.Matrix4x4
import kotlin.math.asin
import kotlin.math.atan2

object AzimuthCalculator {
    private fun transformationMatrixOfShapeRepresentation(shapeRepresentation: ShapeRepresentation): Matrix4x4 {
        return Matrix4x4.fromArray(shapeRepresentation.transformationMatrix.flatten().toDoubleArray())
    }

    fun calculateWallAzimuth(building: Building, floor: Floor, wall: Wall): Double {
        val buildingCardinalDirectionRotationMatrix = Matrix4x4.makeRotationY(building.rotationYCorrectionAngle)
        val buildingTransformationMatrix = transformationMatrixOfShapeRepresentation(building.shapeRepresentation)
        val floorTransformationMatrix = transformationMatrixOfShapeRepresentation(floor.shapeRepresentation)
        val wallTransformationMatrix = transformationMatrixOfShapeRepresentation(wall.shapeRepresentation)

        val wallWorldTransformationMatrix = buildingTransformationMatrix.clone().multiply(floorTransformationMatrix).multiply(wallTransformationMatrix)
        val orientedWallWorldMatrix = buildingCardinalDirectionRotationMatrix.clone().multiply(wallWorldTransformationMatrix)

        val forwardX = orientedWallWorldMatrix.elements[8]
        val forwardZ = orientedWallWorldMatrix.elements[10]

        val azimuthRadians = atan2(-forwardX, forwardZ)
        var azimuthDegrees = Math.toDegrees(azimuthRadians)

        if (azimuthDegrees < 0) {
            azimuthDegrees += 360
        }

        return azimuthDegrees
    }

    fun calculateRoofAreaAzimuth(building: Building, floor: Floor, roofArea: RoofArea): Double {
        val buildingCardinalDirectionRotationMatrix = Matrix4x4.makeRotationY(building.rotationYCorrectionAngle)
        val buildingTransformationMatrix = transformationMatrixOfShapeRepresentation(building.shapeRepresentation)
        val floorTransformationMatrix = transformationMatrixOfShapeRepresentation(floor.shapeRepresentation)
        val wallTransformationMatrix = transformationMatrixOfShapeRepresentation(roofArea.shapeRepresentation)

        val wallWorldTransformationMatrix = buildingTransformationMatrix.clone().multiply(floorTransformationMatrix).multiply(wallTransformationMatrix)
        val orientedWallWorldMatrix = buildingCardinalDirectionRotationMatrix.clone().multiply(wallWorldTransformationMatrix)

        val forwardX = orientedWallWorldMatrix.elements[8]
        val forwardZ = orientedWallWorldMatrix.elements[10]

        val azimuthRadians = atan2(-forwardX, forwardZ)
        var azimuthDegrees = Math.toDegrees(azimuthRadians)

        if (azimuthDegrees < 0) {
            azimuthDegrees += 360
        }

        return azimuthDegrees
    }

    fun calculateRoofAreaPitch(building: Building, floor: Floor, roofArea: RoofArea): Double {
        val buildingCardinalDirectionRotationMatrix = Matrix4x4.makeRotationY(building.rotationYCorrectionAngle)
        val buildingTransformationMatrix = transformationMatrixOfShapeRepresentation(building.shapeRepresentation)
        val floorTransformationMatrix = transformationMatrixOfShapeRepresentation(floor.shapeRepresentation)
        val wallTransformationMatrix = transformationMatrixOfShapeRepresentation(roofArea.shapeRepresentation)

        val wallWorldTransformationMatrix = buildingTransformationMatrix.clone().multiply(floorTransformationMatrix).multiply(wallTransformationMatrix)
        val orientedWallWorldMatrix = buildingCardinalDirectionRotationMatrix.clone().multiply(wallWorldTransformationMatrix)

        val forwardY = orientedWallWorldMatrix.elements[9]

        val pitchRadians = asin(forwardY)
        val pitchDegrees = Math.toDegrees(pitchRadians)

        return pitchDegrees
    }

    fun calculateOpeningAzimuth(building: Building, floor: Floor, wall: Wall, opening: ConstructionPart): Double {
        return calculateWallAzimuth(building, floor, wall)
    }

    fun wallToCardinalDirection(building: Building, floor: Floor, wall: Wall): CardinalDirectionType {
        return azimuthToCardinalDirection(calculateWallAzimuth(building, floor, wall))
    }

    fun roofAreaToCardinalDirection(building: Building, floor: Floor, roofArea: RoofArea): CardinalDirectionType {
        return azimuthToCardinalDirection(calculateRoofAreaAzimuth(building, floor, roofArea))
    }

    fun azimuthToCardinalDirection(azimuth: Double): CardinalDirectionType {
        val index = Math.round(azimuth / 45).toInt() % 8
        return CardinalDirectionType.entries[index]
    }
}