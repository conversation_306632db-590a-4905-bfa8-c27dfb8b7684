package com.doorbit.bff

import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.i
import com.doorbit.bff.core.domain.extension.w
import com.doorbit.bff.core.domain.model.listing.toListingId
import com.doorbit.bff.infra.adapter.listing.CachingListingAdapter
import com.doorbit.bff.infra.listing.repo.ListingRepository
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.lang.Thread.startVirtualThread
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

@Service
class CacheWarmupService(
    private val listingRepository: ListingRepository,
    private val cachingListingAdapter: CachingListingAdapter,
) {

    private val executor = Executors.newFixedThreadPool(8)

    @EventListener(ApplicationReadyEvent::class)
    private fun warmCaches() {
        try {
            startVirtualThread { warmCachesInternal() }
        } catch (ex: Exception) {
            LOGGER.w(ex) { "Couldn't warm caches on startup" }
        }
    }

    /**
     * Alle 2 Stunden per CRON
     */
    @Scheduled(cron = "0 0 */2 * * *")
    fun warmCachesScheduled() {
        try {
            warmCachesInternal()
        } catch (ex: Exception) {
            LOGGER.w(ex) { "Couldn't warm caches on scheduled task" }
        }
    }

    private fun warmCachesInternal() {
        LOGGER.i { "Warming BFF caches .." }
        val start = System.currentTimeMillis()

        warmFlowListings("doorbit_esg")
        LOGGER.i { "Warmed doorbit_esg flow listings in ${System.currentTimeMillis() - start}ms" }

        val start2 = System.currentTimeMillis()
        warmFlowListings("renaldo_objektaufnahme")
        LOGGER.i { "Warmed renaldo_objektaufnahme flow listings in ${System.currentTimeMillis() - start2}ms" }

        LOGGER.i { "Warmed all caches in ${System.currentTimeMillis() - start}ms." }
    }

    private fun warmFlowListings(flowId: String) {
        val flowListings = listingRepository.search(flowId = flowId, limit = 1000)
        val tasks = flowListings.map {
            executor.submit {
                try {
                    val id = it.id.id.toListingId()
                    cachingListingAdapter.fetchListingById(id)
                    cachingListingAdapter.findScannedBuilding(id)
                } catch (ex: Exception) {
                    LOGGER.w(ex) { "Couldn't warm cache entry for listing ${it.id.id}" }
                }
            }
        }

        tasks.forEach { it.get(5, TimeUnit.MINUTES) }
    }

    companion object : WithLogger()


}