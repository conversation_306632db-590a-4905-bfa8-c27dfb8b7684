package com.doorbit.bff.infra.listing.adapter.locationdescription.mapper

object OpenAiApartmentFloorTranslator {
    fun translateFloorNumber(floor: Int): String {
        return when (floor) {
            in -1..0 -> "EG"
            1 -> "1. OG"
            2 -> "2. OG"
            3 -> "3. OG"
            4 -> "4. OG eines Mehrfamilienhauses"
            5 -> "5. OG eines Mehrfamilienhauses"
            else -> "$floor. Stock eines Mehrfamilienhauses"
        }
    }

}
