package com.doorbit.bff.infra.extension

import com.doorbit.bff.infra.config.GEOMETRY_FACTORY
import org.locationtech.jts.geom.Geometry
import org.locationtech.jts.geom.MultiPolygon
import org.locationtech.jts.geom.Polygon
import org.locationtech.jts.io.WKBWriter
import org.slf4j.Logger
import org.slf4j.LoggerFactory

private val LOGGER: Logger = LoggerFactory.getLogger(Geometry::class.java)

fun Geometry.toWKB(): ByteArray {
    val writer = WKBWriter() //TODO constructor parameters?
    return writer.write(this)
}

fun <G : Geometry> G.nullIfInvalid(): G? {
    return if (isValid) this else null
}

fun Geometry.toMultiPolygonArray(): MultiPolygonArray {
    return when (this) {
        is Polygon -> {
            toMultiPolygon().toMultiDimensionalArray()
        }

        is MultiPolygon -> {
            toMultiDimensionalArray()
        }

        else -> {
            emptyArray() //this should never happen
        }
    }
}

fun Geometry.toMultiPolygon(): MultiPolygon? {
    return when (this) {
        is Polygon -> {
            GEOMETRY_FACTORY.createMultiPolygon(arrayOf(this))
        }

        is MultiPolygon -> {
            this
        }

        else -> {
            LOGGER.warn("Geometry is neither Polygon nor MultiPolygon")
            null
        }
    }
}