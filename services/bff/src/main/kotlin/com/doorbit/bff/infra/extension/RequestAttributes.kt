package com.doorbit.bff.infra.extension

import org.springframework.web.context.request.RequestAttributes
import org.springframework.web.context.request.ServletRequestAttributes

fun RequestAttributes.getHeader(name: String): String? {
    if (this is ServletRequestAttributes) {
        return this.request.getHeader(name)
    }
    return null
}

fun RequestAttributes.getBooleanHeader(name: String): Boolean {
    if (this is ServletRequestAttributes) {
        return this.request.getHeader(name)?.toBoolean() ?: false
    }
    return false
}
