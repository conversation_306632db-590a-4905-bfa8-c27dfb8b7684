package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.d
import com.doorbit.bff.infra.extension.MdcThread.startMdcForwardingVirtualThread
import com.doorbit.bff.infra.listing.adapter.fileuploads.UserFileUploadService
import com.doorbit.bff.infra.listing.infrastructureservice.GoogleStorageBucketListingImageStorageService
import com.doorbit.bff.infra.listing.model.fileupload.FileStorageAdapter
import com.doorbit.bff.infra.listing.model.fileupload.URLAndFilename
import com.doorbit.bff.infra.listing.model.listing.Listing
import com.doorbit.bff.infra.listing.support.ZipArchiver
import org.springframework.stereotype.Service
import org.springframework.util.MimeType

@Service
class ListingImageDownloadService(
    private val fileStorageAdapter: FileStorageAdapter,
    private val imageStorageService: ListingImageStorageService,
    private val userFileUploadService: UserFileUploadService,
) {
    fun createDownloadLink(listing: Listing): URLAndFilename? {
        if (listing.assets.images().isEmpty()) {
            return null
        }

        LOGGER.d { "Downloading images into ByteArrays" }
        val images = listing.assets.images()
        val filesAsByteArray = images.parallelStream().map {
            fileStorageAdapter.getFileContents(it.original.path.filename)
        }.toList()

        LOGGER.d { "Converting images to JPEG" }
        val imagesAsJpeg = filesAsByteArray.map { imageStorageService.convertToJpeg(it, MimeType.valueOf("image/webp")) }

        LOGGER.d { "Creating zip archive" }
        val namedImages = imagesAsJpeg.mapIndexed { index, bytes ->
            val name = buildImageFileName(images[index], listing, index)
            name to bytes
        }
        val zipArchive = ZipArchiver.createNamedZipArchive(namedImages)

        val fileName = "listing-${listing.id.id}.zip"

        val fullFileName = GoogleStorageBucketListingImageStorageService.determineFolderName(listing.id)

        return userFileUploadService.uploadFile(fullFileName, zipArchive, "application/zip", "zip", fileName = fileName)
    }

    fun deleteFile(imageDownloadZipLink: URLAndFilename?) {
        if (imageDownloadZipLink == null) {
            return
        }
        startMdcForwardingVirtualThread { fileStorageAdapter.deleteFile(imageDownloadZipLink.filename) }
    }

    fun archiveExists(imageDownloadZipLink: URLAndFilename): Boolean {
        return fileStorageAdapter.getWritableResource(imageDownloadZipLink.filename).exists()
    }

    private fun buildImageFileName(image: com.doorbit.bff.infra.listing.model.listing.Image, listing: Listing, index: Int): String {
        val building = listing.building ?: return "image$index.jpeg"
        val caption = (image.imageInfo?.caption ?: image.original.name).lowercase()
        val floors = building.floors
        for (floor in floors) {
            val floorName = floor.levelInfo.createDisplayName()
            for (room in floor.rooms) {
                val roomName = room.roomName()
                if (caption.contains(floorName.lowercase()) && caption.contains(roomName.lowercase())) {
                    val sanitizedFloor = floorName.replace(" ", "_").replace(".", "")
                    val sanitizedRoom = roomName.replace(" ", "_")
                    return "${sanitizedFloor}_${sanitizedRoom}_Image${index + 1}.jpeg"
                }
            }
        }
        return "image$index.jpeg"
    }

    companion object : WithLogger()
}
