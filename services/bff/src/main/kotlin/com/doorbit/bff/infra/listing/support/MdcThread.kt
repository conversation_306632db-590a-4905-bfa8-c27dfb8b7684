package com.doorbit.bff.infra.listing.support

import org.slf4j.MDC

object MdcThread {
    fun startMdcForwardingVirtualThread(runnable: Runnable) {
        val contextMap = MDC.getCopyOfContextMap()
        Thread.startVirtualThread {
            try {
                MDC.setContextMap(contextMap)
                runnable.run()
            } finally {
                MDC.clear()
            }
        }
    }
}
