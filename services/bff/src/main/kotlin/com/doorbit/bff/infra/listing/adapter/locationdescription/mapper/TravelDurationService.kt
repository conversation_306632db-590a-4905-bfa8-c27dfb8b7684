package com.doorbit.bff.infra.listing.adapter.locationdescription.mapper

import java.time.Duration

/**
 * IF YOU CHANGE THIS LOGIC
 *
 * Please also consider changing it in the same class in BFF.
 *
 * This service calculates the travel duration for different travel types.
 * The travel duration is calculated based on the distance in meters and for sake of precision the average speed per second is used.
 *
 * The mapping between the travel type and the average speed per second is based on taken samples out of Google Maps route planner.
 * For each travel type, several samples have been taken into consideration, varying in distance and city.
 * For walking and bike travel type, the difference of travel times in a metropolitan and a small city is not significant (5%) and can be neglected. This is especially the case because the calculated travel time by Google Maps
 * assumes an average speed of 16km/h, which varies in reality between 10km/h and 20km/h anyways.
 *
 * For CAR travel timing, the calculation is slightly more sophisticated.
 * We assume that a travel distance between 0 and 7km is within the city or in the outskirts of the city and calculate a travel distance
 * of 400m per minute for that distance, which is a good average for both, metropolitan and small cities and takes a bit of traffic into
 * consideration as well.
 *
 * From 7km to 10km we assume that the travel is on the country road and calculate a travel distance of 800m per minute.
 * Everything from 10km and above is calculated with 1600m per minute and is assumed to be on the highway.
 *
 */
object TravelDurationService {

    private const val AVERAGE_WALKING_DISTANCE_PER_MIN = 80
    private const val AVERAGE_WALKING_DISTANCE_PER_SEC = AVERAGE_WALKING_DISTANCE_PER_MIN / 60.0

    private const val AVERAGE_BIKE_DISTANCE_PER_MIN = 230
    private const val AVERAGE_BIKE_DISTANCE_PER_SEC = AVERAGE_BIKE_DISTANCE_PER_MIN / 60.0

    private const val AVERAGE_CAR_DISTANCE_PER_MIN_CITY = 400
    private const val AVERAGE_CAR_DISTANCE_PER_SEC_CITY = AVERAGE_CAR_DISTANCE_PER_MIN_CITY / 60.0
    private const val AVERAGE_CAR_DISTANCE_PER_MIN_COUNTRYSIDE = 800
    private const val AVERAGE_CAR_DISTANCE_PER_SEC_COUNTRYSIDE = AVERAGE_CAR_DISTANCE_PER_MIN_COUNTRYSIDE / 60.0
    private const val AVERAGE_CAR_DISTANCE_PER_MIN_HIGHWAY = 1600
    private const val AVERAGE_CAR_DISTANCE_PER_SEC_HIGHWAY = AVERAGE_CAR_DISTANCE_PER_MIN_HIGHWAY / 60.0

    private val CAR_DURATION_CITY_6000 = calculateCityDurationByCar(6000)
    private val CAR_DURATION_COUNTRYSIDE_3000 = calculateCountryRoadDurationbyCar(3000)

    /**
     * IF YOU CHANGE THIS LOGIC
     *
     * Please also consider changing it in the same class in BFF.
     */
    fun calculateTravelDurationByFeet(distanceM: Int): Duration {
        val timeInSeconds = distanceM / AVERAGE_WALKING_DISTANCE_PER_SEC
        return Duration.ofSeconds(timeInSeconds.toLong())
    }

    /**
     * IF YOU CHANGE THIS LOGIC
     *
     * Please also consider changing it in the same class in BFF.
     */
    fun calculateTravelDurationByBike(distanceM: Int): Duration {
        val timeInSeconds = distanceM / AVERAGE_BIKE_DISTANCE_PER_SEC
        return Duration.ofSeconds(timeInSeconds.toLong())
    }

    /**
     * IF YOU CHANGE THIS LOGIC
     *
     * Please also consider changing it in the same class in BFF.
     */
    fun calculateTravelDurationByCar(distanceM: Int): Duration {
        if (distanceM <= 6000) {
            return calculateCityDurationByCar(distanceM)
        }

        if (distanceM <= 10000) {
            return CAR_DURATION_CITY_6000.plus(calculateCountryRoadDurationbyCar(distanceM - 6000))
        }

        val highwayDuration = calculateHighwayDurationByCar(distanceM - 10000)

        return CAR_DURATION_CITY_6000
            .plus(CAR_DURATION_COUNTRYSIDE_3000)
            .plus(highwayDuration)
    }

    private fun calculateHighwayDurationByCar(distanceM: Int): Duration {
        val timeInSeconds = distanceM / AVERAGE_CAR_DISTANCE_PER_SEC_HIGHWAY
        return Duration.ofSeconds(timeInSeconds.toLong())
    }

    private fun calculateCountryRoadDurationbyCar(distanceM: Int): Duration {
        val timeInSeconds = distanceM / AVERAGE_CAR_DISTANCE_PER_SEC_COUNTRYSIDE
        return Duration.ofSeconds(timeInSeconds.toLong())
    }

    private fun calculateCityDurationByCar(distanceM: Int): Duration {
        val timeInSeconds = distanceM / AVERAGE_CAR_DISTANCE_PER_SEC_CITY
        return Duration.ofSeconds(timeInSeconds.toLong())
    }
}