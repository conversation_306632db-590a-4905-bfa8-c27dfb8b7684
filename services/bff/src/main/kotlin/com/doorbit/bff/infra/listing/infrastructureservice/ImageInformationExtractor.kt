package com.doorbit.bff.infra.listing.infrastructureservice

import com.drew.imaging.ImageMetadataReader
import com.drew.metadata.MetadataException
import com.drew.metadata.exif.ExifIFD0Directory
import com.drew.metadata.exif.ExifIFD0Directory.TAG_ORIENTATION
import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.e
import java.io.InputStream


object ImageInformationExtractor : WithLogger() {

    /**
     * Reads the orientation tag from the image EXIF data, if any.
     */
    fun readImageOrientationTag(inputStream: InputStream): ImageOrientation? {
        val metadata = ImageMetadataReader.readMetadata(inputStream)

        val directory = metadata.getFirstDirectoryOfType(ExifIFD0Directory::class.java) ?: return null

        if (!directory.containsTag(TAG_ORIENTATION)) return null

        return try {
            ImageOrientation(orientation = directory.getInt(TAG_ORIENTATION))
        } catch (me: MetadataException) {
            LOGGER.e { "Could not read orientation tag from image EXIF data: ${me.message}" }
            null
        }
    }

}