package com.doorbit.bff.infra.extension

import org.springframework.core.io.buffer.DataBuffer
import org.springframework.core.io.buffer.DataBufferUtils
import org.springframework.http.codec.multipart.Part
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono


fun Flux<Part>.convertToByteArray(): Mono<ByteArray> {
    val partContents = flatMap(Part::content)
    val mergedContent = DataBufferUtils.join(partContents)
    return mergedContent.map(DataBuffer::toByteArray)
}