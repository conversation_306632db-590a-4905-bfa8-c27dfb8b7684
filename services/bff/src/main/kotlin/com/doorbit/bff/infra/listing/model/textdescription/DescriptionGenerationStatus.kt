package com.doorbit.bff.infra.listing.model.textdescription

import com.doorbit.bff.infra.listing.model.textdescription.DescriptionGenerationStatus.Status.FAILED
import com.doorbit.bff.infra.listing.model.textdescription.DescriptionGenerationStatus.Status.GENERATING
import com.doorbit.bff.infra.listing.model.textdescription.DescriptionGenerationStatus.Status.RETRYING

data class DescriptionGenerationStatus(val status: Status, val retries: Int = 0) {

    fun isGenerating(): Boolean = status == GENERATING || status == RETRYING

    fun countFailure(): DescriptionGenerationStatus {
        val newStatus = if (retries <= 3) RETRYING else FAILED
        return copy(status = newStatus, retries = retries + 1)
    }

    enum class Status {
        NOT_STARTED,
        GENERATING,
        RETRYING,
        FINISHED,
        FAILED
    }

}
