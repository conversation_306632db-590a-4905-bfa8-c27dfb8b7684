package com.doorbit.bff.infra.repository.userprofile

import com.doorbit.bff.infra.adapter.user.UserId
import com.doorbit.bff.core.domain.model.user.profile.UserProfile
import com.doorbit.bff.core.domain.model.user.profile.UserProfileRepository
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.index.Index
import org.springframework.data.mongodb.core.indexOps
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.isEqualTo
import org.springframework.http.HttpStatus.NOT_FOUND
import org.springframework.stereotype.Repository
import org.springframework.web.server.ResponseStatusException

@Repository
class UserProfileMongoRepository(
    @Qualifier("userProfileMongoTemplate") val mongoTemplate: MongoTemplate
) : UserProfileRepository {

    override fun save(userProfile: UserProfile): UserProfile {

        return mongoTemplate.save(userProfile)
    }

    override fun findById(userId: UserId): UserProfile {
        return mongoTemplate.findOne(idQuery(userId), UserProfile::class.java)
            ?: throw ResponseStatusException(NOT_FOUND, "User profile not found")
    }

    override fun findByEmail(email: String): UserProfile? =
        mongoTemplate.findOne(Query.query(Criteria.where("email").isEqualTo(email)), UserProfile::class.java)

    override fun existsById(userId: String): Boolean {
        val query = idQuery(userId)
        return mongoTemplate.exists(query, UserProfile::class.java)
    }


    @EventListener(ApplicationReadyEvent::class)
    fun setupIndices() {
        val indexOps = mongoTemplate.indexOps<UserProfile>()
        indexOps.ensureIndex(Index().named("creationDate").on("creationDate", Sort.Direction.ASC))
        indexOps.ensureIndex(Index().named("email").on("email", Sort.Direction.ASC))
    }

    private fun idQuery(id: UserId) = Query(Criteria.where("_id").isEqualTo(id).and("deleted").isEqualTo(false))
}
