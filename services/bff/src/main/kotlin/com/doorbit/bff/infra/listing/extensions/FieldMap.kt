package com.doorbit.bff.infra.listing.extensions

import com.doorbit.bff.infra.listing.model.listing.ListingField
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldName

fun Map<FieldName, List<ListingField>>?.stringSingleValue(fieldName: String): String? {
    return this?.get(fieldName)?.firstOrNull()?.value as? String
}

fun Map<FieldName, List<ListingField>>?.booleanSingleValue(fieldName: String): Boolean? {
    return this?.get(fieldName)?.firstOrNull()?.value as? Boolean
}

inline fun <reified T : Enum<T>> Map<FieldName, List<ListingField>>?.enumSingleValue(fieldName: String): T? {
    return this?.get(fieldName)?.firstOrNull()?.value?.let { enumValueOf<T>(it as String) }
}

fun Map<FieldName, List<ListingField>>?.doubleSingleValue(fieldName: String): Double? {
    return this?.get(fieldName)?.firstOrNull()?.value as? Double
}

fun Map<FieldName, List<ListingField>>.containsField(fieldName: String): Boolean {
    return this.containsKey(fieldName)
}

fun Map<FieldName, List<ListingField>>?.firstValueOrNull(fieldName: String): Any? {
    return this?.get(fieldName)?.firstOrNull()?.value
}

fun Map<FieldName, List<ListingField>>?.booleanTrueOrNull(fieldName: String): Boolean? {
    return (this?.get(fieldName)?.firstOrNull()?.value as? Boolean)?.takeIf { it }
}