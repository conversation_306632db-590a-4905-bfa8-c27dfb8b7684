package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.core.domain.applicationservice.userprofile.UserProfileService
import com.doorbit.bff.core.domain.model.user.profile.UserProfile
import com.doorbit.bff.infra.adapter.googlechat.ChatAdapter
import com.doorbit.bff.infra.listing.adapter.geolocalization.GeoLocalizationGeoApiAdapter
import com.doorbit.bff.infra.adapter.userprofile.dto.UserProfileGetDto
import com.doorbit.bff.infra.listing.model.export.evebi.EvebiImageExporter
import com.doorbit.bff.infra.listing.model.export.evebi.EvebiImageExporter.ImageReference
import com.doorbit.bff.infra.listing.model.export.evebi.EvebiXmlGeneratorService
import com.doorbit.bff.infra.listing.model.fileupload.FileStorageAdapter
import com.doorbit.bff.infra.listing.model.listing.Listing
import com.doorbit.bff.infra.listing.model.listing.ListingId
import com.doorbit.bff.infra.listing.model.listingenrichment.ListingEnrichmentRepository
import com.doorbit.bff.infra.listing.repo.ListingRepository
import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.e
import org.springframework.stereotype.Service
import org.springframework.util.MimeType
import java.io.File
import java.io.FileOutputStream
import java.io.StringWriter
import java.nio.file.Files
import java.security.MessageDigest
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.xml.bind.JAXBContext
import javax.xml.bind.Marshaller
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi


@Service
class ListingEvexExportService(
    private val listingRepository: ListingRepository,
    private val listingEnrichmentRepository: ListingEnrichmentRepository,
    private val geoLocalizationGeoApiAdapter: GeoLocalizationGeoApiAdapter,
    private val userprofileService: UserProfileService,
    private val fileStorageAdapter: FileStorageAdapter,
    private val listingImageStorageService: ListingImageStorageService,
    private val chatAdapter: ChatAdapter,
) {

    fun exportEvexAsString(listingId: ListingId) : String {
        val listing = listingRepository.findById(listingId)
        val enrichment = listingEnrichmentRepository.findById(listingId)
        val generatorService = EvebiXmlGeneratorService(
            listing,
            userprofileService.getUserProfile(listing.userId),
            geoLocalizationGeoApiAdapter.findAdminBoundaries(enrichment?.geoLocalization?.adminBoundaryIds),

        )
        val xml = generatorService.generateXml()

        val context = JAXBContext.newInstance(xml.javaClass)

        val marshaller = context.createMarshaller()
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true)
        marshaller.setProperty(Marshaller.JAXB_FRAGMENT, false)
        marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8")

        val writer = StringWriter()
        marshaller.marshal(xml, writer)
        return writer.toString()
    }

    @OptIn(ExperimentalEncodingApi::class)
    fun exportEvexAsZipBase64(listingId: ListingId): String {
        val listing = listingRepository.findById(listingId)
        val enrichment = listingEnrichmentRepository.findById(listingId)
        val userProfile = userprofileService.getUserProfile(listing.userId)

        try {
            val generatorService = EvebiXmlGeneratorService(listing, userProfile, geoLocalizationGeoApiAdapter.findAdminBoundaries(enrichment?.geoLocalization?.adminBoundaryIds))
            val toMarshall = generatorService.generateXml()
            val imagesToAppend = EvebiImageExporter(listing, userProfile).decorateEvebiProjektWithImages(toMarshall, listing)
            val context = JAXBContext.newInstance(toMarshall.javaClass)
            val marshaller = context.createMarshaller()
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true)
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, false)
            marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8")

            val xmlFile = File.createTempFile("projekt", ".xml")
            xmlFile.outputStream().use { marshaller.marshal(toMarshall, it) }

            val fileHashes = mutableListOf<String>()
            fileHashes.add("projekt.xml=sha1:${calculateSha1(xmlFile)}")

            val tempZipFile = File.createTempFile("export", ".zip")
            ZipOutputStream(FileOutputStream(tempZipFile)).use { zipOut ->
                addFileToZip(zipOut, xmlFile, "projekt.xml")

                for (image in imagesToAppend) {
                    val imageName = "blob_{${image.id}}.jpg"
                    val imageFile = File.createTempFile("image_${image.id}", ".jpg")
                    val jpegByteArray = getImageBytes(image) ?: continue
                    imageFile.writeBytes(jpegByteArray)
                    addFileToZip(zipOut, imageFile, imageName)
                    fileHashes.add("$imageName=sha1:${calculateSha1(imageFile)}")
                }

                val hashesFile = File.createTempFile("file-hashes", "")
                hashesFile.writeText(fileHashes.joinToString("\n"))
                addFileToZip(zipOut, hashesFile, "file-hashes")
            }

            val zipBytes = Files.readAllBytes(tempZipFile.toPath())
            return Base64.encode(zipBytes)
        } catch (e: Exception) {
            sendChatMessage(listing, userProfile, e)
            throw e
        } finally {
            chatAdapter.sendGenericMessage("EVEX exported for listing ${listing.id}. Listing owner: ${userProfile?.email}")
        }
    }

    private fun sendChatMessage(listing: Listing, userProfile: UserProfile, e: Exception) {
        val userInfo = userProfile?.let { it.email }
        chatAdapter.sendGenericMessage("Failed to EVEX-Export listing ${listing.id} for user $userInfo. Exception was: ${e.message}")
    }

    private fun getImageBytes(image: ImageReference): ByteArray? {
        if (image.bytes != null) {
            return image.bytes
        }

        try {
            val webpImage = fileStorageAdapter.getFileContents(image.image!!.original.path.filename)
            val jpegByteArray = listingImageStorageService.convertToJpeg(webpImage, MimeType.valueOf("image/webp"))
            return jpegByteArray
        } catch (e: Exception) {
            LOGGER.e(e) { "Failed to get image bytes for image ${image.id}" }
            return null
        }
    }

    fun addFileToZip(zipOut: ZipOutputStream, file: File, zipEntryName: String) {
        zipOut.putNextEntry(ZipEntry(zipEntryName))
        file.inputStream().use { it.copyTo(zipOut) }
        zipOut.closeEntry()
    }

    fun calculateSha1(file: File): String {
        val digest = MessageDigest.getInstance("SHA-1")
        file.inputStream().use { inputStream ->
            val buffer = ByteArray(1024)
            var bytesRead: Int
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                digest.update(buffer, 0, bytesRead)
            }
        }
        return digest.digest().joinToString("") { "%02x".format(it) }
    }

    fun generateUniqueId(): String {
        return java.util.UUID.randomUUID().toString().uppercase()
    }

    companion object : WithLogger()

}