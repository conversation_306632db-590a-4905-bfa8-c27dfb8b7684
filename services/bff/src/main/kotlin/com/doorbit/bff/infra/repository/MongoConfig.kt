package com.doorbit.bff.infra.repository

import com.mongodb.client.MongoClient
import com.mongodb.client.MongoClients
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory

@Configuration
class MongoConfig(
    @Value("\${application.mongodb.base-uri}") val mongoUriBase: String
) {

    // Erzeugt einen einzelnen, geteilten MongoClient, der für alle Templates genutzt wird.
    private val mongoClient: MongoClient by lazy {
        MongoClients.create(mongoUriBase)
    }

    /**
     * Factory-Methode zur Erzeugung eines MongoTemplate für einen beliebigen Datenbanknamen.
     */
    fun createMongoTemplate(databaseName: String): MongoTemplate {
        return MongoTemplate(SimpleMongoClientDatabaseFactory(mongoClient, databaseName))
    }

    @Bean
    fun userProfileMongoTemplate(): MongoTemplate = createMongoTemplate("userprofile")

    @Primary
    @Bean
    fun listingMongoTemplate(): MongoTemplate = createMongoTemplate("listing")

}