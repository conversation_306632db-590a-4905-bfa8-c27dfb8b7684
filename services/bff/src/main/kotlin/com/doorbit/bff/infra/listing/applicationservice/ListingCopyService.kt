package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.core.domain.applicationservice.sharedlistings.SharedListingsService
import com.doorbit.bff.infra.listing.model.cacheeviction.CacheEvictionAdapter
import com.doorbit.bff.infra.listing.model.fileupload.FileStorageAdapter
import com.doorbit.bff.infra.listing.model.listing.*
import com.doorbit.bff.infra.listing.repo.ListingRepository
import com.doorbit.bff.infra.listing.repo.ScannedBuildingHistoryRepository
import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.w
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import java.lang.Thread.startVirtualThread
import java.time.Instant

@Service
class ListingCopyService(
    private val listingRepository: ListingRepository,
    private val listingIdFactory: ListingIdFactory,
    private val scannedBuildingHistoryRepository: ScannedBuildingHistoryRepository,
    private val fileStorageAdapter: FileStorageAdapter,
    private val cacheEvictionAdapter: CacheEvictionAdapter,
    private val sharedListingsService: SharedListingsService,
) {

    /**
     * Kopiert ein Listing. Keiner der Parameter muss gesetzt sein, außer ListingId.
     */
    fun copyListing(listingId: ListingId, toUserId: UserId?, toGroupId: String?, flowId: String? = null): Listing {
        val listing = listingRepository.findById(listingId)
        val newListingId = listingIdFactory.newId()
        val newListing = listing.copy(
            id = newListingId,
            userId = toUserId ?: listing.userId,
            billingUserId = toUserId ?: listing.userId,
            billingGroupId = toGroupId ?: listing.billingGroupId,
            flowId = flowId ?: listing.flowId,
            creationDate = Instant.now(),
        )

        listingRepository.save(newListing)

        copyBuildingScanHistory(listingId, newListingId)

        if (newListing.billingGroupId != null) {
            sharedListingsService.setDefaultListingShares(
                listingId = com.doorbit.bff.core.domain.model.listing.ListingId(newListing.id.id)
            )
        }

        if (newListing.assets.images().isNotEmpty()) {
            startVirtualThread {
                val clonedImages = newListing.assets.images().parallelStream().map {
                    copyImage(it, listingId, newListingId)
                }.toList().filterNotNull()

                newListing.assets.replaceImages(clonedImages)

                listingRepository.save(newListing)
                cacheEvictionAdapter.evictListingCache(newListingId)
                cacheEvictionAdapter.evictUserListings(newListing.userId, newListing.flowId)
            }
        }

        cacheEvictionAdapter.evictListingCache(newListingId)
        cacheEvictionAdapter.evictUserListings(newListing.userId, newListing.flowId)

        return newListing
    }

    private fun copyBuildingScanHistory(listingId: ListingId, newListingId: ListingId) {
        val historyItem = scannedBuildingHistoryRepository.getPreviousState(listingId)

        if (historyItem != null) {
            scannedBuildingHistoryRepository.save(
                historyItem.copy(
                    id = ObjectId(),
                    listingId = newListingId.id
                )
            )

            val newHistoryItem = historyItem.copy(
                id = ObjectId(),
                listingId = newListingId.id
            )

            scannedBuildingHistoryRepository.save(newHistoryItem)
        }
    }

    private fun copyImage(it: Image, listingId: ListingId, newListingId: ListingId): Image? {
        val newImage = it.changeFileNames(listingId.id, newListingId.id)

        try {
            fileStorageAdapter.copyFile(it.original.path.filename, newImage.original.path.filename)
            fileStorageAdapter.copyFile(it.thumbnail.path.filename, newImage.thumbnail.path.filename)
            fileStorageAdapter.copyFile(it.mini.path.filename, newImage.mini.path.filename)
        } catch (e: Exception) {
            LOGGER.w(e) { "Failed to copy image: ${it.original.path.filename}" }
            return null
        }

        return newImage
    }

    companion object : WithLogger()

}