package com.doorbit.bff.infra.listing.model.listing.building

import com.doorbit.bff.infra.listing.extensions.doubleSingleValue
import com.doorbit.bff.infra.listing.extensions.enumSingleValue
import com.doorbit.bff.infra.listing.model.listing.ListingField
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldName
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec.ConstructionTypeField
import com.doorbit.bff.infra.listing.model.listing.fields.ConstructionType
import com.doorbit.bff.infra.listing.model.listing.fields.ConstructionType.BY_U_VALUE

data class RoofArea(
    override val id: String,
    override var displayId: String? = null,
    val shapeRepresentation: ShapeRepresentation,
    var customData: Map<FieldName, List<ListingField>>? = null,
    val area: Double,
    val perimeter: Double,
    val isFlat: <PERSON><PERSON><PERSON>,
    val isTriangular: <PERSON>olean,
    val isRectangular: <PERSON><PERSON><PERSON>,
    val roomId: String,
    val width: Double,
    val height: Double,
) : Identifiable {

    fun calculateMasses() {

        val massData = mapOf(
            FieldSpec.InsideAreaGrossField.fieldName to listOf(ListingField(FieldSpec.InsideAreaGrossField.fieldName, value = area, isComputed = true)),
            FieldSpec.SlabPerimeterField.fieldName to listOf(ListingField(FieldSpec.SlabPerimeterField.fieldName, value = perimeter, isComputed = true)),
        )

        customData = CustomDataSetter.updateCustomData(customData, massData)
    }

    fun width(outside: Boolean = false): Double {
        // TODO wenn outside dann sizeAdjustments (Offset) aufschlagen
        return width
    }

    fun height(outside: Boolean = false): Double {
        // TODO wenn outside dann sizeAdjustments (Offset) aufschlagen
        return height
    }

    /**
     * NGF ohne Berücksichtigung von Dachflächenfenstern (Brutto)
     */
    fun insideGrossArea(): Double = area

    /**
     * TODO BGF ohne Berücksichtigung von Dachflächenfenstern (Brutto)
     */
    fun outsideGrossArea(): Double = area

    // TODO Azimut berechnen. In Evebi für jedermann am Bauteil sichtbar. Zudem wahrscheinlich energetisch relevant in
    // Evebi für DIN 18599 und GEG.
//    fun azimuth(): Double {
//        return 0.0
//    }

    // TODO Himmelsrichtung berechnen. In Evebi für jedermann am Bauteil sichtbar. Zudem wahrscheinlich energetisch relevant in
    // Evebi für DIN 18599 und GEG.
//    fun cardinalDirectionType(): CardinalDirectionType {
//        return CardinalDirectionType.N
//    }

    // TODO Neigungswinkel berechnen. In Evebi für jedermann am Bauteil sichtbar. Zudem wahrscheinlich energetisch relevant in
    //    // Evebi für DIN 18599 und GEG.
//    fun pitch(): Double {
//        return 0.0
//    }

    fun constructionType(fallback: ConstructionType = BY_U_VALUE): ConstructionType {
        return customData.enumSingleValue<ConstructionType>(ConstructionTypeField.fieldName) ?: fallback
    }

    fun uValue(fallback: Double = 5.0): Double {
        return customData?.doubleSingleValue(FieldSpec.RoofAreaUValueField.fieldName) ?: fallback
    }

    /**
     * Purposely 0.0 since roof areas always have a room beneath that repsents volume.
     */
    fun volume(): Double = 0.0

}