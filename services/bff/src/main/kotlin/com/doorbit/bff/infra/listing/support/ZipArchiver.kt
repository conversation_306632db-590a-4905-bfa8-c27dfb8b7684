package com.doorbit.bff.infra.listing.support

import java.io.ByteArrayOutputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

object ZipArchiver {

    fun createZipArchive(files: List<ByteArray>, fileExtension: String): ByteArray {
        val namedFiles = files.mapIndexed { index, file -> "image$index.$fileExtension" to file }
        return createNamedZipArchive(namedFiles)
    }

    fun createNamedZipArchive(files: List<Pair<String, ByteArray>>): ByteArray {
        val baos = ByteArrayOutputStream()
        val zos = ZipOutputStream(baos)
        files.forEach { (name, file) ->
            val zipEntry = ZipEntry(name)
            zipEntry.size = file.size.toLong()
            zos.putNextEntry(zipEntry)
            zos.write(file)
            zos.closeEntry()
        }
        zos.close()
        return baos.toByteArray()
    }

}
