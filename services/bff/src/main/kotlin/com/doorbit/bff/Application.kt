package com.doorbit.bff

import com.doorbit.bff.core.domain.support.ApplicationService
import com.doorbit.bff.core.domain.support.DomainService
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.FilterType
import org.springframework.scheduling.annotation.EnableScheduling

@EnableScheduling
@EnableCaching
@ComponentScan(
    includeFilters = [ComponentScan.Filter(
        type = FilterType.ANNOTATION,
        value = [ApplicationService::class, DomainService::class]
    )]
)
@SpringBootApplication
class Application

fun main(args: Array<String>) {
    runApplication<Application>(args = args)
}
