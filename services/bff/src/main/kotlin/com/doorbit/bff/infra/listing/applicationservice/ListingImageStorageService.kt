package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.infra.listing.model.fileupload.StoredImageDto
import com.doorbit.bff.infra.listing.model.listing.ListingId
import org.springframework.util.MimeType

interface ListingImageStorageService {

    /**
     * Uploads an image for a listing.
     * @param resizeImage whether the image should be optimized for web or not. Default is true.
     */
    fun uploadListingImage(
        listingId: ListingId,
        imageBytes: ByteArray,
        mimeType: MimeType,
        resizeImage: Boolean = true
    ): StoredImageDto

    fun deleteImages(imageFileNames: List<String>)
    fun convertToJpeg(image: ByteArray, inputMimeType: MimeType) : ByteArray
}