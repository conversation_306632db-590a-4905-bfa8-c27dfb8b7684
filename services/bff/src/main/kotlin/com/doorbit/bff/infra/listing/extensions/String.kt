package com.doorbit.bff.infra.listing.extensions

import com.doorbit.bff.infra.listing.model.listing.ListingId

val NUMBERS_ONLY_REGEX = Regex("\\d+")

fun String.toListingId(): ListingId = ListingId(this)

/**
 * Whether this String consists of digits only.
 */
fun String.isNumeric(): Boolean = this.matches(NUMBERS_ONLY_REGEX)

/**
 * Mainly used with field names that have an array index.
 * // TODO solve differently. Probably refactor the ListingFields map to an array instead to have complex keys.
 */
fun String.withoutArrayIndex(): String {
    return this.substringBefore("[")
}