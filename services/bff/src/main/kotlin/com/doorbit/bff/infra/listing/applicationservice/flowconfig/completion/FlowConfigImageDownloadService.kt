package com.doorbit.bff.infra.listing.applicationservice.flowconfig.completion

import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.i
import com.doorbit.bff.core.domain.extension.w
import java.net.URI
import java.util.*

object FlowConfigImageDownloadService : WithLogger() {

    fun downloadImageAsBase64(imageUrl: String): String? {
        // Ermittelt die Größe des Bildes in Bytes
        val urlConnection = URI(imageUrl).toURL().openConnection()
        val imageSize = urlConnection.contentLength

        // Prüft, ob das Bild größer als 100 KB ist (100 KB = 102400 Bytes)
        if (imageSize > 102400) {
            LOGGER.w { "Image from $imageUrl is quite large. Please reduce its file size since it will be downloaded to all clients machines." +
                    "File size: ${imageSize / 1024}kb" }
        }

        // <PERSON><PERSON><PERSON> das Bild herunter
        val imageBytes = urlConnection.getInputStream().use { it.readBytes() }

        // Ermittelt den Mime Type des Bildes
        val mimeType = urlConnection.contentType

        // Kodiert das Bild in Base64
        val base64Image = Base64.getEncoder().encodeToString(imageBytes)

        // Kombiniert den Mime Type mit dem Base64-String in einem Data-URI
        return "data:$mimeType;base64,$base64Image"
    }
}