package com.doorbit.bff.infra.listing.adapter.locationdescription

import com.doorbit.bff.infra.listing.adapter.locationdescription.mapper.AITextGenerationMapper
import com.doorbit.bff.infra.listing.model.listing.Listing
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec.ListingRegionField
import com.doorbit.bff.infra.listing.model.listing.fields.ListingRegionType
import com.doorbit.bff.infra.listing.model.listingenrichment.ListingEnrichment
import com.doorbit.bff.infra.listing.model.textdescription.DescriptionConfig
import com.doorbit.bff.infra.listing.model.textdescription.TextDescriptionAdapter
import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.d
import com.doorbit.bff.core.domain.extension.e
import com.doorbit.bff.core.domain.extension.t
import com.fasterxml.jackson.databind.ObjectMapper
import com.theokanning.openai.Usage
import com.theokanning.openai.completion.chat.ChatCompletionRequest
import com.theokanning.openai.completion.chat.ChatMessage
import com.theokanning.openai.completion.chat.ChatMessageRole
import com.theokanning.openai.service.OpenAiService
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.convert.DurationStyle
import org.springframework.stereotype.Component

@Component
class TextDescriptionOpenAIAdapter(
    @Value(value = "\${application.description-generation.open-ai.token}") val openAiToken: String,
    @Value(value = "\${application.description-generation.open-ai.timeout}") val openAiTimeout: String,
    @Value(value = "\${application.description-generation.open-ai.enabled}") val openAiEnabled: Boolean,
    @Value(value = "\${application.description-generation.open-ai.simulate-latency:0}") val latency: Int?,
    private val objectMapper: ObjectMapper,
) : TextDescriptionAdapter {

    private companion object : WithLogger()

    override fun fetchLocationDescription(listing: Listing, listingEnrichment: ListingEnrichment, descriptionConfig: DescriptionConfig, isRetry: Boolean): String {

        if (!openAiEnabled || !listing.shouldGenerateTextDescription()) {
            LOGGER.t { "OpenAI is disabled or not running for this request" }
            latency?.also { Thread.sleep(it.toLong()) }
            return "Eine tolle Immobilie mit hardcoded Location Description, da Open-AI im local Profil disabled ist! Hier steht noch etwas mehr, damit wir die Mindestlänge erreichen."
        }

        val api = constructAPI()

        // Attractiveness POI types but with more than 1 POI per type
        val attractivenessJson = objectMapper.writeValueAsString(AITextGenerationMapper.mapToAITextGeneratorLocationInput(listingEnrichment.geoLocalization))

        LOGGER.t { "OpenAI input for location description: $attractivenessJson" }

        try {
            val completionRequest = ChatCompletionRequest.builder()
                .model("gpt-4o-mini-2024-07-18")
                .messages(
                    listOf(
                        ChatMessage(
                            ChatMessageRole.SYSTEM.value(),
                            """
                                Du bist ein lokaler Immobilien-Exposé Experte, der ansprechende Lagebeschreibungen für Immobilien für ein Immobiliensuchportal erstellt. 
Die Beschreibungen basieren im Wesentlichen auf Points Of Interests (POIs). 
Du bekommst dafür ein JSON mit zu vielen POIs, die Du auswerten sollst und nur die relevanten POIs für die 
Beurteilung der Lage und ihrer Attraktivität in einem gut verständlichen Text zusammenfassen sollst. 
Die Lagebeschreibung muss zwischen 170 und maximal 200 Wörter bestehen. Dabei sollen POI Namen vereinzelt eingestreut werden, um die lokale Lagekenntnis deutlich zu machen. Die Lagebeschreibung soll keine 
Informationen weder zur Energieeffizienz noch zu sonstigen baulichen Besonderheiten 
enthalten. Bitte vermeide, Negatives zu schreiben. Gehe auch auf Freizeit- und Natur-Erholungsmöglichkeiten 
in der Umgebung ein. Es darf auch mit Absätzen gearbeitet werden. Entfernungsangaben passt Du an den Lesefluss an, so dass Du sowohl umgangssprachliche Angaben wie fußläufig als auch zeitliche Angaben und/oder Entfernungsangaben für die Beschreibung verwendest. 
 
Die Sprache des Textes muss ${listing.fields[ListingRegionField]?.regionLanguage ?: "deutsch"} (${listing.fields[ListingRegionField]?.country ?: "Deutschland"}) sein.
                            """.trimIndent()

                        ),
                        ChatMessage(ChatMessageRole.USER.value(), attractivenessJson)
                    )
                )
                .maxTokens(500)
                .frequencyPenalty(0.0)
                .presencePenalty(0.0)
                .topP(1.0)
                .temperature(1.0)
                .user(listing.userId)
                .build()

            val start = System.currentTimeMillis()

            val response = api.createChatCompletion(completionRequest)
            LOGGER.d { "GPT request for location text creation costed tokens:\n" + extractTokensLog(response.usage) }
            val choices = response.choices

            val end = System.currentTimeMillis()
            LOGGER.t { "OpenAI took ${end - start}ms to generate location description" }

            return choices.first().message.content.removePrefix("\"").removeSuffix("\"")

        } catch (ex: Exception) {
            LOGGER.e(ex) { "Error while generating description for listing ${listing.id}" }
            return if (!isRetry) fetchLocationDescription(listing, listingEnrichment, descriptionConfig, true) else ""
        }
    }

    override fun fetchTitleDescription(listing: Listing, listingEnrichment: ListingEnrichment, descriptionConfig: DescriptionConfig, isRetry: Boolean): String {
        if (!openAiEnabled || !listing.shouldGenerateTextDescription()) {
            LOGGER.t { "OpenAI is disabled or not running for this request" }
            latency?.also { Thread.sleep(it.toLong()) }
            return "Eine tolle Immobilie mit hardcoded Title, da Open-AI im local Profil disabled ist!"
        }

        val attractivenessJson = objectMapper.writeValueAsString(AITextGenerationMapper.mapToAITextGeneratorLocationInput(listingEnrichment.geoLocalization))
        val json = objectMapper.writeValueAsString(AITextGenerationMapper.maptoAiTextGeneratorListingDescriptionInput(listing.fields.data().entries.associate { it.key to it.value.firstOrNull() }))

        val input = "$json,$attractivenessJson"

        val prompt = "Du bist ein Bot, der ansprechende Inseratstitel für Immobilien für ein Immobiliensuchportal erstellt. Die Beschreibungen basieren im Wesentlichen auf von Usern gepflegten Fakten der Immobilie, sowie POIs (Mikrolage der Immobilie). Du musst aus den zur Verfügung stehenden Daten einen Titel formulieren, der die angebotene Immobilie gut und ansprechend beschreibt. Der Titel muss zwischen 50 und maximal 90 Zeichen lang sein." +
                "\n" +
                "Die Sprache des Textes muss ${listing.fields[ListingRegionField]?.regionLanguage ?: "deutsch"} (${listing.fields[ListingRegionField]?.country ?: "Deutschland"}) sein."

        LOGGER.t { "OpenAI input for title description: $input" }

        try {
            val api = constructAPI()
            val completionRequest = ChatCompletionRequest.builder()
                .model("gpt-4o-mini-2024-07-18")
                .messages(
                    listOf(
                        ChatMessage(ChatMessageRole.SYSTEM.value(), prompt),
                        ChatMessage(ChatMessageRole.USER.value(), input)
                    )
                )
                .maxTokens(200)
                .frequencyPenalty(0.0)
                .presencePenalty(0.0)
                .topP(1.0)
                .temperature(0.5)
                .build()

            val start = System.currentTimeMillis()

            val choices = api.createChatCompletion(completionRequest).choices

            val end = System.currentTimeMillis()
            LOGGER.t { "OpenAI took ${end - start}ms to generate listing title" }

            return choices.first().message.content.removePrefix("\"").removeSuffix("\"")

        } catch (ex: Exception) {
            LOGGER.e(ex) { "Error while generating listing title for listing ${listing.id}" }
            return if (!isRetry) fetchTitleDescription(listing, listingEnrichment, descriptionConfig, true) else ""
        }
    }

    override fun fetchListingDescription(listing: Listing, listingEnrichment: ListingEnrichment, descriptionConfig: DescriptionConfig, isRetry: Boolean): String {

        if (!openAiEnabled || !listing.shouldGenerateTextDescription()) {
            LOGGER.t { "OpenAI is disabled or not running for this request" }
            latency?.also { Thread.sleep(it.toLong()) }
            return "Eine tolle Immobilie mit hardcoded Object Description, da Open-AI im local Profil disabled ist! Hier steht noch etwas mehr, damit wir die Mindestlänge erreichen."
        }

        val api = constructAPI()

        val json = objectMapper.writeValueAsString(AITextGenerationMapper.maptoAiTextGeneratorListingDescriptionInput(listing.fields.data().entries.associate { it.key to it.value.firstOrNull() }))

        val prompt = "Du bist ein Bot, der ansprechende Objektbeschreibungen für Immobilien für ein Immobiliensuchportal erstellt. Die Beschreibungen basieren ausschließlich auf von Usern gepflegten Fakten der Immobilie. Du bekommst dafür ein JSON mit Fakten, das Du auswerten sollst und in einen gut verständlichen Text zusammenfassen sollst. Die Objektbeschreibung muss zwischen 135 und maximal 150 Wörter bestehen. Die Objektbeschreibung soll keine hinzuerfundenen Informationen enthalten. Jede Information müssen einzeln für sich ausgewertet werden. Es dürfen ausschließlich die Fakten ausgewertet werden. \n" +
                "\n" +
                "Es darf auch mit Absätzen gearbeitet werden." +
                "\n" +
                "Die Sprache des Textes muss ${listing.fields[ListingRegionField]?.regionLanguage ?: "deutsch"} (${listing.fields[ListingRegionField]?.country ?: "Deutschland"}) sein."

        LOGGER.t { "OpenAI input for listing description: $json" }

        try {
            val completionRequest = ChatCompletionRequest.builder()
                .model("gpt-4o")
                .messages(
                    listOf(
                        ChatMessage(ChatMessageRole.SYSTEM.value(), prompt),
                        ChatMessage(ChatMessageRole.USER.value(), json)
                    )
                )
                .maxTokens(500)
                .frequencyPenalty(0.0)
                .presencePenalty(0.0)
                .topP(1.0)
                .temperature(0.5)
                .build()

            val start = System.currentTimeMillis()

            val response = api.createChatCompletion(completionRequest)
            LOGGER.d { "GPT request for object text creation costed tokens:\n" + extractTokensLog(response.usage) }

            val end = System.currentTimeMillis()
            LOGGER.t { "OpenAI took ${end - start}ms to generate listing description" }

            return response.choices.first().message.content.removePrefix("\"").removeSuffix("\"")

        } catch (ex: Exception) {
            LOGGER.e(ex) { "Error while generating object description for listing ${listing.id}" }
            return if (!isRetry) fetchListingDescription(listing, listingEnrichment, descriptionConfig, true) else ""
        }

    }

    override fun translateText(text: String, forRegion: ListingRegionType, isRetry: Boolean): String {
        if (!openAiEnabled) {
            LOGGER.t { "OpenAI is disabled or not running for this request" }
            latency?.also { Thread.sleep(it.toLong()) }
            return "THis is a hardcoded translation, because OpenAI is disabled in local profile."
        }

        val api = constructAPI()

        val prompt = "Bitte übersetze den folgenden Text zu ${forRegion.regionLanguage}:\n$text"

        try {
            val completionRequest = ChatCompletionRequest.builder()
                .model("gpt-3.5-turbo-1106")
                .messages(
                    listOf(
                        ChatMessage(ChatMessageRole.SYSTEM.value(), "Du bist ein Bot, der Texte für die Immobilienbranche in verschiedene Sprachen übersetzt."),
                        ChatMessage(ChatMessageRole.USER.value(), prompt)
                    )
                )
                .maxTokens(750)
                .frequencyPenalty(0.0)
                .presencePenalty(0.0)
                .topP(1.0)
                .temperature(0.3)
                .build()

            val start = System.currentTimeMillis()

            val choices = api.createChatCompletion(completionRequest).choices

            val end = System.currentTimeMillis()
            LOGGER.t { "OpenAI took ${end - start}ms to translate text" }

            return choices.first().message.content.removePrefix("\"").removeSuffix("\"")

        } catch (ex: Exception) {
            LOGGER.e(ex) { "Error while translating text." }
            return if (!isRetry) translateText(text, forRegion, true) else ""
        }

    }

    private fun constructAPI() = OpenAiService(openAiToken, DurationStyle.detectAndParse(openAiTimeout))

    private fun extractTokensLog(usage: Usage): String {
        return """
            Prompt tokens used: ${usage.promptTokens}
            Completion tokens used: ${usage.completionTokens}
            Total: ${usage.totalTokens}
        """.trimIndent()
    }
}
