package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.d
import com.doorbit.bff.infra.listing.model.listing.ListingId
import com.doorbit.bff.infra.listing.model.listing.ListingIdWithPaginationId
import com.doorbit.bff.infra.listing.model.listingenrichment.ListingEnrichmentRepository
import com.doorbit.bff.infra.listing.repo.ListingRepository
import org.bson.types.ObjectId
import org.springframework.stereotype.Service

@Service
class ListingSearchService(
    private val listingRepository: ListingRepository,
    private val listingEnrichmentRepository: ListingEnrichmentRepository,
) {

    fun search(listingIds: List<ListingId>?, userId: String?, billingGroupIds: List<String>?, paginationId: ObjectId?, limit: Int?, flowId: String?): ListingSearchResult {

        val hits = listingRepository.search(
            listingIds = listingIds,
            userId = userId,
            flowId = flowId,
            billingGroupIds = billingGroupIds,
            paginationId = paginationId,
            limit = limit
        )

        LOGGER.d { "Search found ${hits.size} results by listingIds: $listingIds, userId: $userId, billingGroupIds: $billingGroupIds, paginationId: $paginationId, limit: $limit" }

        return ListingSearchResult(
            count = hits.size,
            hits = hits.map(ListingIdWithPaginationId::id),
            paginationId = hits.lastOrNull()?.paginationId?.toHexString()
        )
    }

    companion object : WithLogger()

}

data class ListingSearchResult(
    val count: Int,
    val hits: List<ListingId>,
    val paginationId: String?,
)
