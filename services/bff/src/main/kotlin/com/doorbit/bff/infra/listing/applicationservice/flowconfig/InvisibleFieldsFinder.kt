package com.doorbit.bff.infra.listing.applicationservice.flowconfig

import com.doorbit.bff.infra.listing.api.dto.*
import com.doorbit.bff.infra.listing.model.listing.ListingField
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldName
import jakarta.validation.Valid

object InvisibleFieldsFinder {

    fun invisibleFields(config: ListingFlowDto, fields: Collection<ListingField>): List<FieldName> {
        val fieldsWithVisibility = config.pagesEdit.flatMap { page ->
            if (!isElementVisible(page.visibilityCondition, fields)) {
                fieldsOfHiddenElement(page.elements)
            } else {
                page.elements.flatMap { findFieldVisibility(it.element, fields) }
            }
        }

        return fieldsWithVisibility
            .filter { field -> fieldDoesntAppearAsVisibleElsewhere(field, fieldsWithVisibility) }
            .map { it.first }
    }

    /**
     * Checks if the field is not visible in any other element.
     * If it is, then remove it from the list of "invisible" fields.
     */
    private fun fieldDoesntAppearAsVisibleElsewhere(field: Pair<FieldName, Boolean>, fieldsWithVisibility: List<Pair<FieldName, Boolean>>) =
        !field.second && fieldsWithVisibility.none { (fieldName, visible) -> fieldName == field.first && visible }

    private fun findFieldVisibility(element: PatternLibraryElementElementDto, fields: Collection<ListingField>): List<Pair<FieldName, Boolean>> {

        return when (element) {
            is BooleanUIElementDto -> listOf(element.fieldId.fieldName to isElementVisible(element.visibilityCondition, fields))
            is NumberUIElementDto -> listOf(element.fieldId.fieldName to isElementVisible(element.visibilityCondition, fields))
            is StringUIElementDto -> listOf(element.fieldId.fieldName to isElementVisible(element.visibilityCondition, fields))
            is DateUIElementDto -> listOf(element.fieldId.fieldName to isElementVisible(element.visibilityCondition, fields))
            is FileUIElementDto -> listOf(element.idFieldId.fieldName to isElementVisible(element.visibilityCondition, fields))
            is SingleSelectionUIElementDto -> listOf(element.fieldId.fieldName to isElementVisible(element.visibilityCondition, fields))
            is ArrayUIElementDto -> {
                if (!isElementVisible(element.visibilityCondition, fields)) {
                    fieldsOfHiddenElement(element.elements)
                } else {
                    element.elements.flatMap { findFieldVisibility(it.element, fields) }
                }
            }

            is CustomUIElementDto -> {
                if (!isElementVisible(element.visibilityCondition, fields)) {
                    fieldsOfHiddenElement(element.elements)
                } else {
                    element.elements?.flatMap { findFieldVisibility(it.element, fields) } ?: emptyList()
                }
            }

            is GroupUIElementDto -> {
                if (!isElementVisible(element.visibilityCondition, fields)) {
                    fieldsOfHiddenElement(element.elements)
                } else {
                    element.elements.flatMap { findFieldVisibility(it.element, fields) }
                }
            }

            is ChipGroupUIElementDto -> {
                if (!isElementVisible(element.visibilityCondition, fields)) {
                    element.chips.flatMap { chip -> listOf(chip.fieldId.fieldName to false) }
                } else {
                    element.chips.flatMap { chip -> findFieldVisibility(chip, fields) }
                }
            }

            else -> emptyList()
        }
    }

    private fun fieldsOfHiddenElement(elements: List<PatternLibraryElementDto>): List<Pair<FieldName, Boolean>> = elements.flatMap { ListingFlowConfigUtil.extractFieldNames(it) }.map { it to false }

    private fun isElementVisible(visibilityCondition: @Valid VisibilityConditionDto?, fields: Collection<ListingField>): Boolean {
        if (visibilityCondition == null) return true

        return when (visibilityCondition) {
            is LODto -> evaluateLogicalOperator(visibilityCondition, fields)
            is RFODto -> evaluateRelationalFieldOperator(visibilityCondition, fields)
            else -> true
        }
    }

    private fun evaluateRelationalFieldOperator(visibilityCondition: RFODto, fields: Collection<ListingField>): Boolean {
        val field = fields.find { it.fieldName == visibilityCondition.fieldId.fieldName }

        return when (visibilityCondition.op) {
            "eq" -> field?.value == postprocessCondition(visibilityCondition.value)
            "ne" -> field?.value != postprocessCondition(visibilityCondition.value)
            "gt" -> (field?.value as? Number) > postprocessCondition(visibilityCondition.value)
            "lt" -> (field?.value as? Number) < postprocessCondition(visibilityCondition.value)
            "gte" -> (field?.value as? Number) >= postprocessCondition(visibilityCondition.value)
            "lte" -> (field?.value as? Number) <= postprocessCondition(visibilityCondition.value)
            "in" -> visibilityCondition.valueList.map(::postprocessCondition).contains(field?.value)
            "nin" -> !visibilityCondition.valueList.map(::postprocessCondition).contains(field?.value)
            else -> throw IllegalArgumentException("Relational operator not supported: ${visibilityCondition.op}")
        }
    }

    private fun postprocessCondition(value: Any?): Any? {
        if (value == null) {
            return null
        }

        val valueStr = value.toString()

        // Convert to Boolean
        if (valueStr == "true" || valueStr == "false") {
            return valueStr.toBoolean()
        }

        // Convert to Int
        try {
            return valueStr.toInt()
        } catch (e: NumberFormatException) {
            // Continue to next check if not an Int
        }

        // Convert to Double
        try {
            return valueStr.toDouble()
        } catch (e: NumberFormatException) {
            // Continue to return the original value if not a Double
        }

        // Return the original value if no conversion is possible
        return value
    }

    private fun evaluateLogicalOperator(visibilityCondition: LODto, fields: Collection<ListingField>): Boolean {
        return when (visibilityCondition.operator) {
            "AND" -> visibilityCondition.conditions.all { isElementVisible(it, fields) }
            "OR" -> visibilityCondition.conditions.any { isElementVisible(it, fields) }
            "NOT" -> visibilityCondition.conditions.none { isElementVisible(it, fields) }
            else -> throw IllegalArgumentException("Logical operator not supported: ${visibilityCondition.operator}")
        }
    }
}

private operator fun Any?.compareTo(value: Any?): Int {
    return when {
        this == null && value == null -> 0
        this == null -> -1
        value == null -> 1
        else -> (this as Comparable<*>).compareTo(value)
    }

}