package com.doorbit.bff.infra.listing.applicationservice.flowconfig.completion

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

object DynamicDateRangeParser {

    fun parseDateNotation(notation: String?): String? {
        if (notation == null || notation.isEmpty()) {
            return null
        }

        if (notation.length > 4 || notation.last() !in setOf('Y', 'M', 'D', 'h', 'm')) {
            // Fixed date was given
            return notation
        }

        val currentDateTime = LocalDateTime.now()

        val sign = notation.first()
        val amount = notation.substring(1, notation.length - 1).toLong()
        val unit = notation.last()

        val datetime = when (unit) {
            'Y' -> if (sign == '+') currentDateTime.plusYears(amount) else currentDateTime.minusYears(amount)
            'M' -> if (sign == '+') currentDateTime.plusMonths(amount) else currentDateTime.minusMonths(amount)
            'D' -> if (sign == '+') currentDateTime.plusDays(amount) else currentDateTime.minusDays(amount)
            'h' -> if (sign == '+') currentDateTime.plusHours(amount) else currentDateTime.minusHours(amount)
            'm' -> if (sign == '+') currentDateTime.plusMinutes(amount) else currentDateTime.minusMinutes(amount)
            else -> throw IllegalArgumentException("Unsupported time unit in notation: $notation")
        }

        return formatDateTime(datetime)
    }

    private val formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm")

    private fun formatDateTime(dateTime: LocalDateTime): String {
        return dateTime.format(formatter)
    }

}