package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.infra.listing.model.listing.Image
import com.doorbit.bff.infra.listing.model.listing.ImageType
import com.doorbit.bff.infra.listing.model.listing.ListingId
import org.springframework.util.MimeType
import java.time.Instant

interface ImageService {

    fun storeImage(listingId: ListingId, imageId: String, image: ByteArray, imageType: ImageType)

    fun uploadImage(listingId: ListingId, image: ByteArray, imageId: String, mimeType: MimeType, creationDate: Instant = Instant.now(), imageType: ImageType): Image
}