package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.infra.listing.api.mapper.ListingObfuscationMapper
import com.doorbit.bff.infra.listing.model.listing.Listing
import com.doorbit.bff.infra.listing.model.listing.service.LocationObfuscater
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class ListingLocationObfuscationService(
    @Value(value = "\${application.fields.location-obfuscation.min-distance}") val minDistance: Int,
    @Value(value = "\${application.fields.location-obfuscation.max-distance}") val maxDistance: Int,
) {

    fun updateLocationObfuscation(listing: Listing, obfuscateLocation: Boolean) {
        val fieldsToSet = if (obfuscateLocation) {
            val obfuscatedLocation = LocationObfuscater.createObfuscatedLocation(listing, minDistance, maxDistance) ?: return
            ListingObfuscationMapper.coordinateToFields(obfuscatedLocation.first, obfuscatedLocation.second)
        } else {
            ListingObfuscationMapper.unsetObfuscation()
        }

        listing.fields.update(fieldsToSet)
    }
}