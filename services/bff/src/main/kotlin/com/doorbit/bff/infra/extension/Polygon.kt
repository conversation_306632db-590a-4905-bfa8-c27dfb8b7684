package com.doorbit.bff.infra.extension

import com.doorbit.bff.infra.config.GEOMETRY_FACTORY
import org.locationtech.jts.geom.MultiPolygon
import org.locationtech.jts.geom.Polygon

typealias PolygonArray = Array<LinearRingArray>
typealias PolygonList = List<LinearRingList>

fun Polygon.toMultiDimensionalArray(): PolygonArray {
    val exteriorRing = exteriorRing.toMultiDimensionalArray()
    return Array(numInteriorRing + 1) { interiorRingIndex ->
        if (interiorRingIndex == 0) {
            exteriorRing
        } else {
            val interiorRing = getInteriorRingN(interiorRingIndex - 1)
            interiorRing.toMultiDimensionalArray()
        }
    }
}

fun Polygon.toMultiDimensionalList(): PolygonList {
    val exteriorRing = exteriorRing.toMultiDimensionalList()
    val interiorRings = MutableList(numInteriorRing + 1) { interiorRingIndex ->
        if (interiorRingIndex == 0) {
            exteriorRing.toList()
        } else {
            val interiorRing = getInteriorRingN(interiorRingIndex - 1)
            interiorRing.toMultiDimensionalList()
        }
    }
    return interiorRings
}

fun Polygon.toMultiPolygon(): MultiPolygon {
    return GEOMETRY_FACTORY.createMultiPolygon(arrayOf(this))
}
