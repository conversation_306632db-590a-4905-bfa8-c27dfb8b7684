package com.doorbit.bff.infra.listing.applicationservice.flowconfig

import com.doorbit.bff.infra.listing.api.dto.ListingFlowDto
import com.doorbit.bff.infra.listing.model.fileupload.FileStorageAdapter
import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.e
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.common.cache.Cache
import com.google.common.cache.CacheBuilder
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.server.ResponseStatusException
import java.util.concurrent.TimeUnit

@Service
class ListingFlowService(
    private val objectMapper: ObjectMapper,
    @Qualifier("flowConfigStorageAdapter") private val fileStorageAdapter: FileStorageAdapter,
) {

    val cache: Cache<String, ListingFlowConfigUtil> = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).build()

    fun storeListingFlow(listingFlowDto: ListingFlowDto) {

        val dtoWithFieldDefinitions = ListingFlowValidator.withResolvedFieldDefinitions(listingFlowDto)

        val writableResource = fileStorageAdapter.getWritableResource("${listingFlowDto.id}.json")
        val outputStream = writableResource.outputStream
        objectMapper.writeValue(outputStream, dtoWithFieldDefinitions)

        fileStorageAdapter.setContentType(writableResource, "application/json")
        cache.put(listingFlowDto.id, ListingFlowConfigUtil(dtoWithFieldDefinitions))
    }

    fun getListingFlowById(flowId: String): ListingFlowConfigUtil {
        return loadFromCache(flowId)
    }

    private fun loadFromCache(flowId: String) = cache.get(flowId) {
        try {
            val config = fileStorageAdapter.getWritableResource("$flowId.json").inputStream.use {
                objectMapper.readValue(it, ListingFlowDto::class.java)
            }
            ListingFlowConfigUtil(config)
        } catch (e: Exception) {
            LOGGER.e(e) { "Error reading flow config for flow $flowId" }
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Listing flow config for flow $flowId not found -> Bad Request.")
        }
    }

    companion object : WithLogger()

}