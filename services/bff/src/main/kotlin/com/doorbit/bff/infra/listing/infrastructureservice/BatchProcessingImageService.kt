package com.doorbit.bff.infra.listing.infrastructureservice

import com.doorbit.bff.infra.listing.adapter.fileuploads.UserFileUploadService
import com.doorbit.bff.infra.listing.applicationservice.ImageService
import com.doorbit.bff.infra.listing.applicationservice.ListingImageStorageService
import com.doorbit.bff.infra.listing.model.cacheeviction.CacheEvictionAdapter
import com.doorbit.bff.infra.listing.model.fileupload.StoredImageDto
import com.doorbit.bff.infra.listing.model.listing.Image
import com.doorbit.bff.infra.listing.model.listing.Image.ImageFile
import com.doorbit.bff.infra.listing.model.listing.ImageType
import com.doorbit.bff.infra.listing.model.listing.ListingId
import com.doorbit.bff.infra.listing.model.listing.images.ImageBatchProcessingRepository
import com.doorbit.bff.infra.listing.model.listing.images.ImageProcessingJob
import com.doorbit.bff.infra.listing.repo.ListingRepository
import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.e
import com.doorbit.bff.core.domain.extension.i
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.core.env.Environment
import org.springframework.core.task.TaskExecutor
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.util.MimeType
import java.io.FileNotFoundException
import java.lang.Thread.startVirtualThread
import java.time.Instant

@Service
class BatchProcessingImageService(
    private val userFileUploadService: UserFileUploadService,
    private val listingRepository: ListingRepository,
    private val listingImageStorageService: ListingImageStorageService,
    private val imageBatchProcessingRepository: ImageBatchProcessingRepository,
    private val cacheEvictionAdapter: CacheEvictionAdapter,
    @Value(value = "\${application.user-uploads.images.batch-processing-concurrency}") private val concurrency: Int,
    private val imageProcessingExecutor: TaskExecutor,
    private val environment: Environment
) : ImageService {

    override fun storeImage(listingId: ListingId, imageId: String, image: ByteArray, imageType: ImageType) {

        if (imageBatchProcessingRepository.hasEntryForImage(imageId)) {
            LOGGER.i { "Image $imageId for listing $listingId is already scheduled for processing. Dropping request." }
            return
        }

        imageProcessingExecutor.execute {
            // Store in temp directory first
            userFileUploadService.uploadFile("temp/", image, "application/octet-stream", "tmp", "$imageId.tmp")
        }

        LOGGER.i { "Stored image $imageId for listing $listingId in temp folder." }

        // Remember that we have to do something with the image
        val newJob = ImageProcessingJob(
            listingId = listingId.id,
            imageId = imageId,
            imageType = imageType,
        )

        imageBatchProcessingRepository.save(newJob)
    }

    @Scheduled(initialDelay = 5000, fixedDelay = 1000)
    fun processJobs() {

        if (environment.activeProfiles.contains("local-to-integ")) {
            return
        }

        IntRange(1, concurrency).map {
            startVirtualThread {
                while (true) {
                    val nextJob = imageBatchProcessingRepository.findAndProcessNextScheduledJobs() ?: return@startVirtualThread
                    val listingId = ListingId(nextJob.listingId)
                    val imageId = nextJob.imageId

                    LOGGER.i { "Scheduled processing of image $imageId for listing $listingId" }

                    try {
                        val image = userFileUploadService.downloadFile("temp/", "$imageId.tmp")

                        uploadNewImage(listingId, image, imageId, MimeType.valueOf("image/jpeg"), nextJob.imageType)

                        LOGGER.i { "Image $imageId for listing $listingId processed and uploaded" }

                        imageBatchProcessingRepository.succeeded(nextJob)

                        userFileUploadService.deleteFile("temp/", "$imageId.tmp")

                    } catch (fnf: FileNotFoundException) {
                        LOGGER.i { "Image not available yet, trying again" }
                    } catch (e: Exception) {
                        LOGGER.e(e) { "Failed to process image $imageId for listing $listingId" }
                        imageBatchProcessingRepository.failed(nextJob, e.toString())
                    }
                }
            }
        }.forEach(Thread::join)
    }

    private fun uploadNewImage(listingId: ListingId, image: ByteArray, id: String, mimeType: MimeType, imageType: ImageType) {
        val listing = listingRepository.findById(listingId)
        val newImage = uploadImage(listingId, image, id, mimeType, imageType = imageType)

        // Check again because the image conversion process takes time and in between there could be more images
        if (!listing.assets.canMoreImagesBeAdded()) {
            return
        }

        listingRepository.storeImageUrl(listingId, newImage)

        cacheEvictionAdapter.evictListingCache(listingId)
    }

    override fun uploadImage(listingId: ListingId, image: ByteArray, imageId: String, mimeType: MimeType, creationDate: Instant, imageType: ImageType): Image {
        val storedImage: StoredImageDto = listingImageStorageService.uploadListingImage(
            listingId = listingId,
            imageBytes = image,
            mimeType = mimeType,
        )

        val imageModel = storedImage.let {
            Image(
                id = imageId,
                imageType = imageType,
                original = ImageFile(it.name, it.pathOriginal),
                thumbnail = ImageFile("${it.name}_thumbnail", it.pathThumbnail),
                mini = ImageFile("${it.name}_mini", it.pathMini),
                originalImageWidth = it.width,
                originalImageHeight = it.height,
                uploadDate = creationDate
            )
        }
        return imageModel
    }

    companion object : WithLogger()
}