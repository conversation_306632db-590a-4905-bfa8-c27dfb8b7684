package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.infra.listing.model.listing.GeoLocalizationAdapter
import com.doorbit.bff.infra.listing.model.listing.Listing
import com.doorbit.bff.infra.listing.model.listing.ListingId
import com.doorbit.bff.infra.listing.model.listingenrichment.ListingEnrichment
import com.doorbit.bff.infra.listing.model.listingenrichment.ListingEnrichmentRepository
import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.t
import org.springframework.stereotype.Service

@Service
class ListingEnrichmentService(
    private val listingEnrichmentRepository: ListingEnrichmentRepository,
    private val geoLocalizationAdapter: GeoLocalizationAdapter,
) {

    private companion object : WithLogger()

    fun updateListingEnrichment(listing: Listing) {
        listingEnrichmentRepository.deleteById(listing.id)
        fetchAndStoreSingleEnrichment(listing, insertInDB = true)
    }

    /**
     * Fetches a PropertyEnrichment from the GeoLocalizationAdapter and returns it. Optionally it can be inserted into the DB.
     * throws DuplicateKeyException if the PropertyEnrichment already exists in the DB.
     */
    fun fetchAndStoreSingleEnrichment(listingToEnrich: Listing, insertInDB: Boolean = false): ListingEnrichment? {
        LOGGER.t { "Fetching enrichment for listing ${listingToEnrich.id}" }
        val geoLocalization = geoLocalizationAdapter.fetchGeoLocalizationFor(listingToEnrich) ?: return null
        val enrichment = ListingEnrichment(listingToEnrich.id, geoLocalization)
        if (insertInDB) listingEnrichmentRepository.insert(enrichment)
        return enrichment
    }

    fun deleteById(listingId: ListingId) {
        listingEnrichmentRepository.deleteById(listingId)
    }
}
