package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.infra.listing.applicationservice.flowconfig.ListingFlowService
import com.doorbit.bff.infra.listing.model.listing.Listing
import com.doorbit.bff.infra.listing.model.listing.ListingField
import com.doorbit.bff.infra.listing.model.listing.ListingId
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldName
import com.doorbit.bff.infra.listing.model.listing.fieldsuggestion.FieldSuggester
import com.doorbit.bff.infra.listing.model.listing.fieldsuggestion.FieldSuggestionService
import com.doorbit.bff.infra.listing.model.listing.fieldsuggestion.ListingChanges
import com.doorbit.bff.infra.listing.repo.ListingRepository
import org.springframework.stereotype.Service
import kotlin.reflect.KClass

@Service
class ListingFieldsService(
    val listingRepository: ListingRepository,
    val listingWorkflowService: ListingWorkflowService,
    val fieldSuggestionService: FieldSuggestionService,
    val flowConfigService: ListingFlowService
) {

    /**
     * Adds new fields or updates existing ones.
     * Will run suggesters after.
     *
     * @param skippedSuggesters Suggesters that should not be run after the fields are updated.
     *                          This is used for invocations of this method made by a suggester directly.
     *                          The suggester that has just run should not be run again.
     */
    fun updateFields(
        listingId: ListingId? = null,
        newFields: Map<FieldName, List<ListingField>>,
        listing: Listing? = null,
        skipSuggesters: Boolean = false
    ) {
        val aListing = listing ?: listingRepository.findById(listingId!!)
        val oldFields = aListing.fields.data()
        aListing.fields.update(newFields)
        aListing.fields.deleteInvisibleFields(flowConfigService.getListingFlowById(aListing.flowId))

        val updateResult = ListingFieldUpdateResult(oldFields, newFields)

        listingRepository.save(aListing)
        listingWorkflowService.listingUpdated(aListing = aListing, updateResult = updateResult)

        if (skipSuggesters) {
            return
        }

        fieldSuggestionService.updateSuggestions(aListing, ListingChanges(hasBuildingGeometryChanged = false, updateResult.touchedFields()))
    }

}