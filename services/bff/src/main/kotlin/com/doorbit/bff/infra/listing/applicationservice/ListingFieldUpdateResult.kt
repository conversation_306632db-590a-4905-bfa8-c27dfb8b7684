package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.infra.listing.model.listing.ListingField
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldName
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec.AddressShowObfuscatedLocationField

data class ListingFieldUpdateResult(val oldFields: Map<FieldName, List<ListingField>>, val newFields: Map<FieldName, List<ListingField>>) {

    fun isFieldChanged(fn: FieldName): Boolean {
        val newFieldList = newFields[fn]
        val oldFieldList = oldFields[fn]

        if (newFieldList == null) {
            return false
        }

        if (oldFieldList == null) {
            return true // New field is set. If it was not set before, it is a change.
        }

        if (newFieldList.size != oldFieldList.size) {
            return true // Unterschiedliche <PERSON> bedeutet eine Änderung
        }

        for (i in newFieldList.indices) {
            if (!newFieldList[i].equalsWithValue(oldFieldList[i])) {
                return true
            }
        }

        return false
    }

    fun hasChanged(): Boolean {
        // Überprüft, ob irgendein Feld geändert wurde
        return oldFields.keys.any { isFieldChanged(it) } || newFields.keys.any { isFieldChanged(it) }
    }

    fun isListingLocationChanged(): Boolean {
        return FieldSpec.listingLocationFieldNames.any(::isFieldChanged)
    }

    fun getObfuscatedLocationSetting(): Boolean {
        if (!isFieldChanged(AddressShowObfuscatedLocationField.fieldName)) {
            return oldFields[AddressShowObfuscatedLocationField.fieldName]?.first()?.value as? Boolean ?: false
        }

        // If its changed but not present in the new fields, it was removed and should be considered false
        return newFields[AddressShowObfuscatedLocationField.fieldName]?.first()?.value as? Boolean ?: false
    }

    /**
     * Returns a map of deleted fields and their old values.
     */
    fun getDeletedFields(): Map<FieldName, List<ListingField>> {
        val deletedFields = mutableMapOf<FieldName, List<ListingField>>()

        // Iterate through newFields to find fields with null value
        newFields.forEach { (fieldName, fields) ->
            fields.forEach { field ->
                if (field.value == null) {
                    // If the field has a null value in newFields, get the old value from oldFields
                    val oldValue = oldFields[fieldName]?.filter { it.index == field.index }
                    if (oldValue != null && oldValue.isNotEmpty()) {
                        deletedFields[fieldName] = oldValue
                    }
                }
            }
        }

        return deletedFields
    }

    fun touchedFields(): Set<FieldName> {
        val addedFieldNames = newFields.keys - oldFields.keys
        val removedFieldNames = getDeletedFields().keys
        val changedFields = newFields.keys.filter(::isFieldChanged)

        return addedFieldNames + removedFieldNames + changedFields
    }
}