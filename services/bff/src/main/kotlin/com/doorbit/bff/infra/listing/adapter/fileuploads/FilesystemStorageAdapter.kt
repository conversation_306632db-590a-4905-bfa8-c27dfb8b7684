package com.doorbit.bff.infra.listing.adapter.fileuploads

import com.doorbit.bff.infra.listing.model.fileupload.FileStorageAdapter
import com.doorbit.bff.core.domain.extension.WithLogger
import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.Resource
import org.springframework.core.io.WritableResource
import java.io.File
import java.net.URL
import java.nio.file.Files
import kotlin.io.path.Path

class FilesystemStorageAdapter : FileStorageAdapter {

    private companion object : WithLogger()

    override fun getWritableResource(fullFileName: String): FileSystemResource {
        val basePath = "tmp${File.separatorChar}uploads${File.separatorChar}"

        val subpath = if (fullFileName.contains("/")) fullFileName.substringBeforeLast("/") else ""

        val dir = Path(basePath + subpath)
        val file = Path(basePath + fullFileName)

        Files.createDirectories(dir)

        return FileSystemResource(file)
    }

    override fun getFileContents(fullFileName: String): ByteArray {
        return getWritableResource(fullFileName).inputStream.readAllBytes()
    }

    override fun setContentType(resource: WritableResource, contentType: String) {
        // Not needed
    }

    override fun getURL(resource: Resource): URL {
        return URL("file", resource.uri.host, resource.uri.path)
    }

    override fun copyFile(srcfullFileName: String, targetFullFileName: String) {
        val src = getWritableResource(srcfullFileName)
        val target = getWritableResource(targetFullFileName)

        src.inputStream.use { srcStream ->
            target.outputStream.use { targetStream ->
                srcStream.copyTo(targetStream)
            }
        }

    }

    override fun deleteFile(fullFileName: String) {
        getWritableResource(fullFileName).file.delete()
    }

    override fun setCacheControl(resource: WritableResource, value: String) {
        // Not needed
    }

}