package com.doorbit.bff.infra.extension

import com.doorbit.bff.core.domain.extension.e
import org.locationtech.jts.geom.Geometry
import org.locationtech.jts.io.WKTReader
import org.slf4j.Logger
import org.slf4j.LoggerFactory

private val LOGGER: Logger = LoggerFactory.getLogger(String::class.java)

fun String.wkt2Geometry(): Geometry? {
    val reader = WKTReader()
    return try {
        reader.read(this)
    } catch (throwable: Throwable) {
        LOGGER.e(throwable) { "Error occured while parsing WKT: $this" }
        null
    }
}
