package com.doorbit.bff.infra.listing.applicationservice.flowconfig.completion

import com.doorbit.bff.infra.listing.api.dto.ArrayUIElementDto
import com.doorbit.bff.infra.listing.api.dto.BooleanUIElementDto
import com.doorbit.bff.infra.listing.api.dto.CustomUIElementDto
import com.doorbit.bff.infra.listing.api.dto.FieldIdDto
import com.doorbit.bff.infra.listing.api.dto.FieldIdWithUnitDto
import com.doorbit.bff.infra.listing.api.dto.GroupUIElementDto
import com.doorbit.bff.infra.listing.api.dto.ListingFlowDto
import com.doorbit.bff.infra.listing.api.dto.NumberUIElementDto
import com.doorbit.bff.infra.listing.api.dto.PatternLibraryElementElementDto
import com.doorbit.bff.infra.listing.api.dto.SingleSelectionUIElementDto
import com.doorbit.bff.infra.listing.api.dto.SingleSelectionUIElementItemDto
import com.doorbit.bff.infra.listing.api.dto.StringUIElementDto
import com.doorbit.bff.infra.listing.api.dto.TranslatableEnumValueDto
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.BooleanDataType
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.DoubleDataType
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.EnumDataType
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.IntDataType
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.StringDataType
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException

object FlowConfigElementCompletionMapper {

    private const val MAX_STRING_LENGTH = 10_000
    private const val MIN_STRING_LENGTH = 0
    private const val MIN_DOUBLE_VALUE = Double.MIN_VALUE
    private const val MAX_DOUBLE_VALUE = Double.MAX_VALUE
    private const val DEFAULT_BOOLEAN_VALUE = false

    fun completeBooleanElement(element: BooleanUIElementDto, treeLevel: Int) {
        if (element.exampleImage != null)
            element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

        element.treeLevel = treeLevel

        val fieldSpec = FieldSpec.byFieldName(element.fieldId.fieldName)

        if (element.defaultValue == null) {
            element.defaultValue = fieldSpec?.defaultValue as? Boolean ?: DEFAULT_BOOLEAN_VALUE
        }
    }

    fun completeStringUiElement(element: StringUIElementDto, treeLevel: Int) {
        if (element.exampleImage != null)
            element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

        element.treeLevel = treeLevel

        if (element.lengthMinimum == null) {
            element.lengthMinimum = FieldSpec.byFieldName(element.fieldId.fieldName)?.validationSpec?.minStringLength ?: MIN_STRING_LENGTH
        }

        if (element.lengthMaximum == null) {
            element.lengthMaximum = FieldSpec.byFieldName(element.fieldId.fieldName)?.validationSpec?.maxStringLength ?: MAX_STRING_LENGTH
        }
    }

    fun completeNumberElement(element: NumberUIElementDto, treeLevel: Int) {
        if (element.exampleImage != null)
            element.exampleImage = FlowConfigImageDownloadService.downloadImageAsBase64(element.exampleImage!!)

        element.treeLevel = treeLevel

        val fieldSpec = FieldSpec.byFieldName(element.fieldId.fieldName)

        if (element.minimum == null) {
            element.minimum = fieldSpec?.validationSpec?.minNumber ?: MIN_DOUBLE_VALUE
        }

        if (element.maximum == null) {
            element.maximum = fieldSpec?.validationSpec?.maxNumber ?: MAX_DOUBLE_VALUE
        }

        if (element.unit == null) {
            element.unit = fieldSpec?.unit?.name
        }
    }

    fun postProcessViewElement(field: FieldIdWithUnitDto, flowDto: ListingFlowDto) {
        field.unit = FieldSpec.byFieldName(field.fieldId.fieldName)?.unit?.name

        if (isSingleSelectionUIElement(field, flowDto)) {
            // Take over all translated enum constants from the EDIT config to the field if it is an enum
            addTranslatedEnumConstantsIfNeeded(field, flowDto)
        }
    }

    private fun isSingleSelectionUIElement(field: FieldIdWithUnitDto, flowDto: ListingFlowDto): Boolean {
        return findSingleSelectOptionsIfAny(flowDto, field).isNotEmpty()
    }

    private fun findSingleSelectOptionsIfAny(
        flowDto: ListingFlowDto,
        field: FieldIdWithUnitDto
    ): List<SingleSelectionUIElementItemDto> {
        return flowDto.pagesEdit
            .flatMap { it.elements }
            .map { it.element }
            .map { findEditSingleSelectOptions(it, field.fieldId) }
            .flatten()
    }

    private fun addTranslatedEnumConstantsIfNeeded(field: @Valid FieldIdWithUnitDto, flowDto: ListingFlowDto) {
        val possibleEnumValues = findSingleSelectOptionsIfAny(flowDto, field)

        if (possibleEnumValues.isEmpty()) throw ResponseStatusException(HttpStatus.BAD_REQUEST, "No enum values found for field ${field.fieldId.fieldName}")

        field.possibleEnumValues = possibleEnumValues.map {
            TranslatableEnumValueDto().apply {
                key = it.key
                translations = it.label
            }
        }
    }

    private fun findEditSingleSelectOptions(pattern: PatternLibraryElementElementDto, fieldId: FieldIdDto): List<SingleSelectionUIElementItemDto> {
        return when (pattern) {
            is SingleSelectionUIElementDto -> listOf(pattern)
            is CustomUIElementDto -> {
                val foundInElements = pattern.elements?.map {
                    val items = findEditSingleSelectOptions(it.element, fieldId)
                    if (items.isNotEmpty()) return items
                    emptyList<SingleSelectionUIElementDto>()
                }?.flatten() ?: emptyList()

                pattern.subFlows?.map { subflow ->
                    subflow.elements.map {
                        val items = findEditSingleSelectOptions(it.element, fieldId)
                        if (items.isNotEmpty()) return items
                        emptyList<SingleSelectionUIElementDto>()
                    }.flatten()
                }?.flatten() ?: emptyList()

                foundInElements
            }

            is ArrayUIElementDto -> pattern.elements.map {
                val items = findEditSingleSelectOptions(it.element, fieldId)
                if (items.isNotEmpty()) return items
                emptyList<SingleSelectionUIElementDto>()
            }.flatten()

            is GroupUIElementDto -> pattern.elements.map {
                val items = findEditSingleSelectOptions(it.element, fieldId)
                if (items.isNotEmpty()) return items
                emptyList<SingleSelectionUIElementDto>()
            }.flatten()

            else -> emptyList()
        }
            .firstOrNull { it.fieldId.fieldName == fieldId.fieldName }
            ?.options ?: emptyList()
    }

}