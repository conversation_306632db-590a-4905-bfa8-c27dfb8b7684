package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.infra.listing.api.dto.FieldDataDataInnerDto
import com.doorbit.bff.infra.listing.api.dto.FieldDataDto
import com.doorbit.bff.infra.listing.model.listing.Listing
import com.doorbit.bff.infra.listing.model.listing.building.Building
import com.doorbit.bff.infra.listing.model.listing.building.ConstructionPartType
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldName
import com.doorbit.bff.infra.listing.model.listing.fieldsuggestion.FieldSuggestionService
import com.doorbit.bff.infra.listing.model.listing.fieldsuggestion.ListingChanges
import com.doorbit.bff.infra.listing.repo.ListingRepository
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.server.ResponseStatusException
import java.lang.reflect.Field

@Service
class BuildingService(
    private val fieldSuggestionService: FieldSuggestionService,
    private val listingRepository: ListingRepository,
) {

    fun setBuildingCustomData(listing: Listing, constructionPartType: ConstructionPartType, fieldDataDto: FieldDataDto, outsidePartsOnly: Boolean = false) {
        if (listing.building == null) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Building is not set")
        }

        val building = listing.building.setCustomDataByConstructionPartType(constructionPartType, fieldDataDto, outsidePartsOnly)
        updateBuilding(building, listing, fieldDataDto.fieldNames())
    }

    fun setBuildingCustomData(listing: Listing, constructionPartId: String, fieldDataDto: FieldDataDto): Building {
        if (listing.building == null) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Building is not set")
        }

        val building = listing.building.setCustomDataByConstructionPartId(constructionPartId, fieldDataDto)
        updateBuilding(building, listing, fieldDataDto.fieldNames())

        return building
    }

    private fun updateBuilding(building: Building, listing: Listing, touchedCustomData: Set<FieldName>) {
        val recalculatedListing = listing.withBuilding(building)
        val newListingFieldsCustomData = building.calculations.toCustomData()
        listing.fields.update(newListingFieldsCustomData)
        fieldSuggestionService.updateSuggestions(listing, ListingChanges(
            hasBuildingGeometryChanged = false,
            touchedListingFields = newListingFieldsCustomData.keys,
            touchedBuildingCustomDataFields = touchedCustomData
        ))

        listingRepository.save(recalculatedListing)

    }

    fun FieldDataDto.fieldNames(): Set<FieldName> {
        return this.data.map { it.fieldName }.toSet()
    }

}