package com.doorbit.bff.infra.extension

import org.locationtech.jts.geom.MultiPolygon
import org.locationtech.jts.geom.Polygon

typealias MultiPolygonArray = Array<PolygonArray>
typealias MultiPolygonList = List<PolygonList>

fun MultiPolygon.toMultiDimensionalArray(): MultiPolygonArray {
    return Array(numGeometries) { multiPolygonGeometryIndex ->
        val polygon = getGeometryN(multiPolygonGeometryIndex) as Polygon
        polygon.toMultiDimensionalArray()
    }
}

fun MultiPolygon.toMultiDimensionalList(): MultiPolygonList {
    return List(numGeometries) { multiPolygonGeometryIndex ->
        val polygon = getGeometryN(multiPolygonGeometryIndex) as Polygon
        polygon.toMultiDimensionalList()
    }
}