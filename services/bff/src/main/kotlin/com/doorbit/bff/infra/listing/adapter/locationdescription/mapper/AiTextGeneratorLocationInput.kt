package com.doorbit.bff.infra.listing.adapter.locationdescription.mapper

import com.doorbit.bff.infra.listing.model.listingenrichment.PoiType

data class AiTextGeneratorLocationInput(
    val achievableScore: Int,
    val score: Int,
    val categories: List<AiTextGeneratorLocationInputGeoAttractivenessCategory>
)

data class AiTextGeneratorLocationInputGeoAttractivenessCategory(
    val categoryKey: String,
    val achievableScore: Int,
    val score: Int,
    val ratingCriteria: List<AiTextGeneratorLocationInputGeoAttractivenessRatingCriteria>
)

data class AiTextGeneratorLocationInputGeoAttractivenessRatingCriteria(
    val key: String,
    val achievableScore: Int,
    val score: Int,
    val pointsOfInterest: List<AiTextGeneratorLocationInputGeoAttractivenessPoi>? = null
)

data class AiTextGeneratorLocationInputGeoAttractivenessPoi(
    val poiType: PoiType,
    val pois: List<AiTextGeneratorLocationInputGeoAttractivenessPoiListItem>
)

data class AiTextGeneratorLocationInputGeoAttractivenessPoiListItem(
    val poiName: String?,
    val available: Boolean,
    val distanceInMeters: Int?,
    val travelTimeByFeet: Int?,
    val travelTimeByBike: Int?,
    val travelTimeByCar: Int?,
)