package com.doorbit.bff.infra.listing.infrastructureservice

import com.doorbit.bff.infra.listing.applicationservice.ListingImageStorageService
import com.doorbit.bff.infra.listing.infrastructureservice.ImageInformationExtractor.readImageOrientationTag
import com.doorbit.bff.infra.listing.model.fileupload.FileStorageAdapter
import com.doorbit.bff.infra.listing.model.fileupload.StoredImageDto
import com.doorbit.bff.infra.listing.model.fileupload.URLAndFilename
import com.doorbit.bff.infra.listing.model.listing.Image
import com.doorbit.bff.infra.listing.model.listing.ListingId
import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.e
import com.doorbit.bff.core.domain.extension.t
import com.luciad.imageio.webp.WebPWriteParam
import com.twelvemonkeys.image.AffineTransformOp
import com.twelvemonkeys.image.ResampleOp
import com.twelvemonkeys.image.ResampleOp.FILTER_BOX
import com.twelvemonkeys.imageio.stream.ByteArrayImageInputStream
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.core.env.Environment
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.util.MimeType
import org.springframework.web.server.ResponseStatusException
import java.awt.Dimension
import java.awt.geom.AffineTransform
import java.awt.image.BufferedImage
import java.io.ByteArrayOutputStream
import java.util.*
import javax.imageio.*
import javax.imageio.stream.ImageInputStream

@Service
class GoogleStorageBucketListingImageStorageService(
    private val fileStorageAdapter: FileStorageAdapter,
    private val environment: Environment
) : ListingImageStorageService {

    @Value("\${application.user-uploads.images.compression}")
    private var compressionLevel: Double = -1.0

    @Value("\${application.user-uploads.images.size-boundaries.original}")
    private var originalBoundarySize: Int = -1

    @Value("\${application.user-uploads.images.size-boundaries.thumbnail}")
    private var thumbnailBoundarySize: Int = -1

    @Value("\${application.user-uploads.images.size-boundaries.mini}")
    private var miniBoundarySize: Int = -1

    @Value("\${application.user-uploads.cache-control}")
    private var cacheHeader: String = ""

    private lateinit var imageBoundariesOriginal: Dimension
    private lateinit var imageBoundariesThumbnail: Dimension
    private lateinit var imageBoundariesMini: Dimension

    @EventListener(ApplicationReadyEvent::class)
    fun initialize() {
        require(compressionLevel > 0 && originalBoundarySize > 0 && thumbnailBoundarySize > 0 && miniBoundarySize > 0)

        imageBoundariesOriginal = Dimension(originalBoundarySize, originalBoundarySize)
        imageBoundariesThumbnail = Dimension(thumbnailBoundarySize, thumbnailBoundarySize)
        imageBoundariesMini = Dimension(miniBoundarySize, miniBoundarySize)
    }

    override fun uploadListingImage(
        listingId: ListingId,
        imageBytes: ByteArray,
        mimeType: MimeType,
        resizeImage: Boolean
    ): StoredImageDto {
        try {
            LOGGER.t { "Uploading image for listing $listingId" }

            validateMimeType(mimeType)

            LOGGER.t { "Reading image from multipart upload" }

            val image = ImageIO.read(ByteArrayImageInputStream(imageBytes))

            return writeImage(image, determineFolderName(listingId), optimizeImage = resizeImage, imageOrientation = readImageOrientationTag(imageBytes.inputStream()))

        } catch (ex: Exception) {
            LOGGER.e(ex) { "Couldn't store image" }

            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Couldn't store images due to", ex)
        }
    }

    override fun deleteImages(imageFileNames: List<String>) {
        LOGGER.t { "Deleting images $imageFileNames" }

        imageFileNames.parallelStream().forEach(fileStorageAdapter::deleteFile)

        LOGGER.t { "Images $imageFileNames deleted" }
    }

    override fun convertToJpeg(image: ByteArray, inputMimeType: MimeType): ByteArray {
        val reader = createImageReader(image, inputMimeType)
        val bufferedImage = (reader.input as ImageInputStream).use { reader.read(0, reader.defaultReadParam) }
        return convertToJpeg(bufferedImage)
    }

    private fun writeImage(image: BufferedImage, folderName: String, optimizeImage: Boolean, imageOrientation: ImageOrientation?): StoredImageDto {

        val fileName = createNewFilename()

        var originalImage = image

        val rotationOp = imageOrientation?.let { createImageRotationOperation(it, originalImage) }
        if (rotationOp != null) {
            LOGGER.t { "Rotating image" }
            originalImage = rotateImage(imageToRotate = originalImage, rotationOp = rotationOp, imageOrientation = imageOrientation)
        }

        val resampledImage = if (optimizeImage) {
            LOGGER.t { "Resampling image" }
            createResamplerOperation(originalImage, strategy = FILTER_BOX).filter(originalImage, null)
        } else image

        val resampledImageThumbnail =
            createResamplerOperation(originalImage, strategy = FILTER_BOX, thumbnail = true).filter(originalImage, null)
        val resampledImageMini = createResamplerOperation(originalImage, strategy = FILTER_BOX, mini = true).filter(originalImage, null)

        LOGGER.t { "Converting to WEBP and storing image" }

        val writer = ImageIO.getImageWritersByFormatName("webp").next()

        val path = convertAndStoreImage(resampledImage, folderName, fileName, writer)
        val pathTh = convertAndStoreImage(resampledImageThumbnail, folderName, "${fileName}_th", writer)
        val pathMini = convertAndStoreImage(resampledImageMini, folderName, "${fileName}_mini", writer)

        return StoredImageDto(
            name = fileName,
            pathOriginal = path,
            pathThumbnail = pathTh,
            pathMini = pathMini,
            width = resampledImage.width,
            height = resampledImage.height
        )
    }

    private fun createImageReader(imageBytes: ByteArray, mimeType: MimeType): ImageReader {
        validateMimeType(mimeType)

        val readers = ImageIO.getImageReadersByMIMEType(mimeType.toString())

        require(readers.hasNext()) { "No ImageIO reader found for: $mimeType" }

        val reader = readers.next()
        reader.input = ByteArrayImageInputStream(imageBytes)

        // Validate
        val imageFormat = reader.formatName
        if (!Image.ALLOWED_IMAGE_TYPES.contains(imageFormat.lowercase())) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Illegal content type $imageFormat")
        }

        return reader
    }

    private fun validateMimeType(mimeType: MimeType) {
        val headerContentType = mimeType.toString()
        if (!Image.ALLOWED_CONTENT_TYPES.contains(headerContentType.lowercase())) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Illegal content type $headerContentType")
        }
    }

    private fun convertAndStoreImage(input: BufferedImage, folder: String, fileName: String, writer: ImageWriter): URLAndFilename {
        val osArchitecture = System.getProperty("os.arch").lowercase()
        val isARMArchitecture = osArchitecture.startsWith("aarch") || osArchitecture.startsWith("arm") //new Macs or Raspis are using ARM-Chips (aarch64 / ARM64)
        val isLocalEnvironment = environment.activeProfiles.contains("local")

        LOGGER.t { "Converting image on OS architecture: $osArchitecture (local: $isLocalEnvironment) …" }

        val fileExtension = if (isARMArchitecture) ".jpeg" else WEBP_FILE_EXTENSION //TODO: use constant as well
        val fullFileName = folder + fileName + fileExtension

        LOGGER.t { "Acquiring writable resource for $fileName" }

        val writableResource = fileStorageAdapter.getWritableResource(fullFileName)

        //TODO: check if image is correctly rotated. not always with kamera images
        if (isARMArchitecture && isLocalEnvironment) { //the ImageIO library to convert jpegs to webp-files doesn't support other os architecture
            val byteArray = convertToJpeg(input)
            writableResource.outputStream.write(byteArray)
        } else {
            val oldOutputStream = writableResource.outputStream

            val newImageOutputStream = ImageIO.createImageOutputStream(oldOutputStream)

            writer.output = newImageOutputStream
            val writeParam = writer.defaultWriteParam as WebPWriteParam
            writeParam.compressionMode = ImageWriteParam.MODE_EXPLICIT
            writeParam.compressionType = writeParam.compressionTypes[WebPWriteParam.LOSSY_COMPRESSION]
            writeParam.compressionQuality = compressionLevel.toFloat()

            LOGGER.t { "Writing webp image …" }

            oldOutputStream.use {
                newImageOutputStream.use {
                    writer.write(null, IIOImage(input, null, null), writeParam)
                }
            }
        }

        LOGGER.t { "Setting content type and cache control" }

        fileStorageAdapter.setContentType(writableResource, "image/webp")
        fileStorageAdapter.setCacheControl(writableResource, cacheHeader)

        LOGGER.t { "Image successfully stored" }

        return URLAndFilename(fileStorageAdapter.getURL(writableResource).toExternalForm(), fullFileName)
    }

    fun convertToJpeg(input: BufferedImage): ByteArray {
        LOGGER.t { "Creating jpeg image …" }
        val byteArrayOutputStream = ByteArrayOutputStream()
        ImageIO.write(input, "jpeg", byteArrayOutputStream)
        byteArrayOutputStream.flush()
        return byteArrayOutputStream.toByteArray()
    }


    private fun createResamplerOperation(
        image: BufferedImage,
        thumbnail: Boolean = false,
        strategy: Int,
        mini: Boolean = false
    ): ResampleOp {
        LOGGER.t { "Resampling original resolution of ${image.width}x${image.height}" }
        val boundaryDimension =
            if (thumbnail) imageBoundariesThumbnail else if (mini) imageBoundariesMini else imageBoundariesOriginal
        val newDimensionsForImage = scaleDimensions(Dimension(image.width, image.height), boundaryDimension)
        return ResampleOp(
            newDimensionsForImage.width,
            newDimensionsForImage.height,
            strategy
        )
    }

    fun scaleDimensions(imgSize: Dimension, boundary: Dimension): Dimension {
        val originalWidth = imgSize.width
        val originalHeight = imgSize.height
        val boundWidth = boundary.width
        val boundHeight = boundary.height
        var finalWidth = originalWidth
        var finalHeight = originalHeight

        // first check if we need to scale width
        if (originalWidth > boundWidth) {
            //scale width to fit
            finalWidth = boundWidth
            //scale height to maintain aspect ratio
            finalHeight = finalWidth * originalHeight / originalWidth
        }

        // then check if we need to scale even with the new height
        if (finalHeight > boundHeight) {
            //scale height to fit instead
            finalHeight = boundHeight
            //scale width to maintain aspect ratio
            finalWidth = finalHeight * originalWidth / originalHeight
        }

        return Dimension(finalWidth, finalHeight)
    }

    private fun rotateImage(imageToRotate: BufferedImage, rotationOp: AffineTransformOp, imageOrientation: ImageOrientation): BufferedImage {
        // Create a rotated version of the original image, hence we have to flip width and height
        val newWidth = if (imageOrientation.orientation > 4) imageToRotate.height else imageToRotate.width
        val newHeight = if (imageOrientation.orientation > 4) imageToRotate.width else imageToRotate.height
        val newImage = BufferedImage(newWidth, newHeight, imageToRotate.type)
        return rotationOp.filter(imageToRotate, newImage)
    }

    private fun createImageRotationOperation(info: ImageOrientation, image: BufferedImage): AffineTransformOp? {
        val t = AffineTransform()
        when (info.orientation) {
            1 -> {
                return null
            }

            2 -> {
                t.scale(-1.0, 1.0)
                t.translate(-image.width.toDouble(), 0.0)
            }

            3 -> {
                t.translate(image.width.toDouble(), image.height.toDouble())
                t.rotate(Math.PI)
            }

            4 -> {
                t.scale(1.0, -1.0)
                t.translate(0.0, -image.height.toDouble())
            }

            5 -> {
                t.rotate(-Math.PI / 2)
                t.scale(-1.0, 1.0)
            }

            6 -> {
                t.translate(image.height.toDouble(), 0.0)
                t.rotate(Math.PI / 2)
            }

            7 -> {
                t.scale(-1.0, 1.0)
                t.translate(-image.height.toDouble(), 0.0)
                t.translate(0.0, image.width.toDouble())
                t.rotate(3 * Math.PI / 2)
            }

            8 -> {
                t.translate(0.0, image.width.toDouble())
                t.rotate(3 * Math.PI / 2)
            }
        }

        return AffineTransformOp(t, AffineTransformOp.TYPE_BICUBIC)
    }

    private fun createNewFilename(): String = UUID.randomUUID().toString()

    companion object : WithLogger() {
        fun determineFolderName(listingId: ListingId): String {
            return listingId.id + "/"
        }

        private const val WEBP_FILE_EXTENSION = ".webp"
    }
}
