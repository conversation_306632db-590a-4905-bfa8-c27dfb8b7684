package com.doorbit.bff.infra.extension

import java.lang.Thread.startVirtualThread
import java.util.concurrent.CompletableFuture

/**
 * Also see https://blogs.oracle.com/javamagazine/post/virtual-threads-futures
 */
class VirtualFuture<R>(
    task: () -> R,
) {
    private val future: CompletableFuture<R> = CompletableFuture()

    init {
        startVirtualThread {
            try {
                future.complete(task())
            } catch (e: Throwable) {
                future.completeExceptionally(e)
            }
        }
    }

    fun join(): R = future.join()
}
