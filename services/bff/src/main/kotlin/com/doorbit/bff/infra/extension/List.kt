package com.doorbit.bff.infra.extension

import com.doorbit.bff.core.domain.extension.e
import com.doorbit.bff.infra.config.GEOMETRY_FACTORY
import org.locationtech.jts.geom.Coordinate
import org.locationtech.jts.geom.LinearRing
import org.locationtech.jts.geom.MultiPolygon
import org.locationtech.jts.geom.Polygon
import org.slf4j.Logger
import org.slf4j.LoggerFactory

private val LOGGER: Logger = LoggerFactory.getLogger(String::class.java)

fun List<List<List<List<Double>>>>.toSafeMultiPolygon(): MultiPolygon? {
    return try {
        toMultiPolygon()
    } catch (throwable: Throwable) {
        LOGGER.e(throwable) { "Error occured while parsing multi dimensional double array: $this" }
        null
    }
}

@Throws
fun List<List<List<List<Double>>>>.toMultiPolygon(): MultiPolygon? {
    if (isEmpty()) {
        return null
    }
    val polygons = mapNotNull { it.toPolygon() }
    if (polygons.isEmpty()) {
        return null
    }
    return GEOMETRY_FACTORY
        .createMultiPolygon(polygons.toTypedArray())
        .nullIfInvalid()
}

@Throws
fun List<List<List<Double>>>.toPolygon(): Polygon? {
    if (isEmpty()) {
        return null
    }
    val exteriorRing = get(0).toLinearRing() ?: return null
    val interiorRingsData = subList(1, size)
    if (interiorRingsData.isEmpty()) {
        return GEOMETRY_FACTORY
            .createPolygon(exteriorRing)
            .nullIfInvalid()
    }
    val interiorRings = interiorRingsData.mapNotNull { interiorRingData ->
        interiorRingData.toLinearRing()
    }
    if (interiorRings.isEmpty()) {
        return GEOMETRY_FACTORY
            .createPolygon(exteriorRing)
            .nullIfInvalid()
    }
    return GEOMETRY_FACTORY
        .createPolygon(exteriorRing, interiorRings.toTypedArray())
        .nullIfInvalid()
}

@Throws
fun List<List<Double>>.toLinearRing(): LinearRing? {
    if (isEmpty()) {
        return null
    }
    val coordinates = mapNotNull { it.toCoordinate() }
    if (coordinates.isEmpty()) {
        return null
    }
    return GEOMETRY_FACTORY
        .createLinearRing(coordinates.toTypedArray())
        .nullIfInvalid()
}

@Throws
fun List<Double>.toCoordinate(): Coordinate? {
    if (size < 2) {
        return null
    }
    val x = get(0)
    val y = get(1)
    return Coordinate(x, y)
}