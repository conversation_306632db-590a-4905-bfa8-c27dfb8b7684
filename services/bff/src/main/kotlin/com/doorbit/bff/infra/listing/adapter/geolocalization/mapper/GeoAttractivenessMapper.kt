package com.doorbit.bff.infra.listing.adapter.geolocalization.mapper

import com.doorbit.bff.infra.adapter.api.geo.dto.AttractivenessCategoryDto
import com.doorbit.bff.infra.adapter.api.geo.dto.AttractivenessDto
import com.doorbit.bff.infra.adapter.api.geo.dto.AttractivenessPoiDto
import com.doorbit.bff.infra.adapter.api.geo.dto.RatingCriteriaDto
import com.doorbit.bff.infra.listing.model.listingenrichment.*

object GeoAttractivenessMapper {

    fun fromDto(attractiveness: AttractivenessDto): GeoAttractiveness {
        return GeoAttractiveness(
            achievableScore = attractiveness.achievableScore,
            score = attractiveness.score,
            categories = attractiveness.categories.map(::fromCategoryDto)
        )
    }

    private fun fromCategoryDto(dto: AttractivenessCategoryDto): GeoAttractivenessCategory {
        return GeoAttractivenessCategory(
            categoryKey = dto.categoryKey,
            achievableScore = dto.achievableScore,
            score = dto.score,
            ratingCriteria = dto.ratingCriteria.map(::fromRatingCriteriaDto)
        )
    }

    private fun fromRatingCriteriaDto(ratingCriteriaDto: RatingCriteriaDto): GeoAttractivenessRatingCriteria {
        return GeoAttractivenessRatingCriteria(
            key = ratingCriteriaDto.key,
            achievableScore = ratingCriteriaDto.achievableScore,
            score = ratingCriteriaDto.score,
            isDataMissing = ratingCriteriaDto.isDataMissing,
            pointsOfInterest = ratingCriteriaDto.pointsOfInterest?.map(::fromPoiDto)
        )
    }

    private fun fromPoiDto(attractivenessPoiDto: AttractivenessPoiDto): GeoAttractivenessPoi {
        return GeoAttractivenessPoi(
            poiType = mapPoiType(attractivenessPoiDto.poiType),
            poiName = attractivenessPoiDto.poiName,
            available = attractivenessPoiDto.available,
            radiusInMeter = attractivenessPoiDto.searchRadiusM,
            distanceInMeters = attractivenessPoiDto.distanceM
        )
    }

    private fun mapPoiType(poiType: String): PoiType {
        return when (poiType) {
            "AQUARIUM" -> PoiType.AQUARIUM
            "AIRPORT_INTERNATIONAL_SELFMAINTAINED" -> PoiType.INTERNATIONAL_AIRPORT
            "AMUSEMENT_PARK_SELFMAINTAINED" -> PoiType.AMUSEMENT_PARK
            "ANIMAL_TRAINING" -> PoiType.ANIMAL_TRAINING
            "BAKERY" -> PoiType.BAKERY
            "BANK" -> PoiType.BANK
            "BAR_PUB" -> PoiType.BAR_PUB
            "BUS_STOP" -> PoiType.BUS_STOP
            "BATHING_PLACE" -> PoiType.BATHING_PLACE
            "BOWLING" -> PoiType.BOWLING_ALLEY
            "CHEMIST" -> PoiType.CHEMIST
            "CHILDCARE" -> PoiType.CHILDCARE
            "CINEMA" -> PoiType.CINEMA
            "DIY" -> PoiType.HARDWARE_STORE
            "DOCTORS" -> PoiType.DOCTORS
            "DOG_PARK" -> PoiType.DOG_PARK
            "ELECTRIC_CAR_CHARGER" -> PoiType.ELECTRIC_CAR_CHARGER
            "FITNESS_CENTER" -> PoiType.FITNESS_CENTER
            "FURNITURE_INTERIOR" -> PoiType.FURNITURE_AND_INTERIOR
            "GARDEN_CENTER" -> PoiType.GARDEN_CENTER
            "HAIRDRESSER" -> PoiType.HAIR_DRESSER
            "HOSPITAL" -> PoiType.HOSPITAL
            "KIOSK" -> PoiType.KIOSK
            "LAKE" -> PoiType.LAKE
            "LIBRARY" -> PoiType.LIBRARY
            "MALL" -> PoiType.MALL
            "MARKETPLACE" -> PoiType.MARKETPLACE
            "METROPOLITAN_CITY" -> PoiType.METROPOLITAN_CITY
            "MOUNTAIN_RANGE" -> PoiType.MOUNTAIN_RANGE
            "MUSIC_SCHOOL" -> PoiType.MUSIC_SCHOOL
            "NATURE_RESERVE" -> PoiType.NATURE_RESERVE
            "NIGHTCLUB" -> PoiType.NIGHTCLUB
            "PARCEL_LOCKERS" -> PoiType.PARCEL_LOCKER
            "PARK" -> PoiType.PARK
            "PHARMACY" -> PoiType.PHARMACY
            "PLACE_OF_WORSHIP" -> PoiType.PLACE_OF_WORSHIP
            "PLAYGROUND" -> PoiType.PLAYGROUND
            "RAILWAY_STOP" -> PoiType.TRAIN_STATION
            "RESTAURANT_AND_CAFE_AND_FASTFOOD" -> PoiType.RESTAURANT_CAFE
            "RIVER_AND_CANAL" -> PoiType.RIVER
            "SAFETY_AMENITIES" -> PoiType.SAFETY_AMENITY
            "SCHOOL" -> PoiType.SCHOOL
            "SEA" -> PoiType.COASTLINE
            "SHOPPING_CENTER_PROCESSED" -> PoiType.SHOPPING_CENTER
            "SPORT_BASKETBALL" -> PoiType.SPORT_BASKETBALL
            "SPORT_GOLF_COURSE" -> PoiType.SPORT_GOLF_COURSE
            "SPORT_HORSE_RIDING" -> PoiType.SPORT_HORSE_RIDING
            "SPORT_ICE_HALL" -> PoiType.SPORT_ICE_RINK
            "SPORT_PADDLE_BOAT" -> PoiType.SPORT_PADDLE_SPORTS
            "SPORT_SOCCER" -> PoiType.SPORT_SOCCER
            "SPORT_SHOOTING" -> PoiType.SPORT_SHOOTING
            "SPORT_SWIMMING" -> PoiType.SPORT_SWIMMING
            "SPORT_TENNIS" -> PoiType.SPORT_TENNIS
            "SPORT_WINTER" -> PoiType.SPORT_WINTER
            "SUPERMARKET" -> PoiType.SUPERMARKET
            "THEATER" -> PoiType.THEATER
            "UNIVERSITY" -> PoiType.UNIVERSITY
            "WATER_PARK" -> PoiType.WATER_PARK
            "WOODLAND_AND_FORESTS" -> PoiType.FOREST
            "ZOO" -> PoiType.ZOO
            else -> throw IllegalArgumentException("Unknown poi type $poiType")
        }

    }
}
