package com.doorbit.bff.infra.listing.adapter.geolocalization.mapper

import com.doorbit.bff.infra.adapter.api.geo.dto.*
import com.doorbit.bff.infra.listing.model.listingenrichment.*
import com.doorbit.bff.infra.listing.model.listingenrichment.PoiType.*

object GeoLocalizationMapper {

    fun fromDto(dto: GeoLocalizationDto): GeoLocalization {
        return GeoLocalization(
            debugIds = dto.debugIds,
            adminBoundaryIds = toAdminBoundaryIds(dto),
            poiGroups = mapToPoiGroups(dto.pointsOfInterest),
            geoAttractiveness = GeoAttractivenessMapper.fromDto(dto.attractiveness),
            zensus = dto.zensus?.let { fromDto(dto.zensus) }
        )
    }

    private fun fromDto(dto : GeoLocalizationZensusDto?) : ZensusData? {
        if (dto == null) return null
        return ZensusData(
            primaryHeatingType = dto.primaryHeatingType?.let { ZensusPrimaryHeatingType(primaryHeatingType = it.type, confidence = it.confidence) },
            primaryEnergySource = dto.primaryEnergySource?.let { ZensusPrimaryEnergySource(primaryEnergySource = it.type, confidence = it.confidence) }
        )
    }

    private fun toAdminBoundaryIds(dto: GeoLocalizationDto): AdminBoundaryIds {
        return AdminBoundaryIds(
            districtBoundaryId = dto.adminBoundaryInfo.district,
            cityBoundaryId = dto.adminBoundaryInfo.city,
            countyBoundaryId = dto.adminBoundaryInfo.county,
            stateBoundaryId = dto.adminBoundaryInfo.state
        )
    }

    private fun mapToPoiGroups(dto: GeoLocalizationPointsOfInterestDto): List<POIGroup> {
        val poiGroups = mutableListOf<POIGroup>()

        mapAtoB(dto, poiGroups)
        mapCtoD(dto, poiGroups)
        mapEtoH(dto, poiGroups)
        mapItoM(dto, poiGroups)
        mapNtoR(dto, poiGroups)
        mapStoT(dto, poiGroups)
        mapUtoZ(dto, poiGroups)

        return poiGroups
    }

    private fun mapUtoZ(dto: GeoLocalizationPointsOfInterestDto, poiGroups: MutableList<POIGroup>) {
        poiGroups += addPoiGroup(poiType = UNIVERSITY, parentPoiGroups = UNIVERSITY.parentGroups, items = dto.universities.items, distance = dto.universities.searchRadius)
        poiGroups += addPoiGroup(poiType = THEATER, parentPoiGroups = THEATER.parentGroups, items = dto.theaters.items, distance = dto.theaters.searchRadius)
        poiGroups += addPoiGroup(poiType = WATER_PARK, parentPoiGroups = WATER_PARK.parentGroups, items = dto.waterParks.items, distance = dto.waterParks.searchRadius)
        poiGroups += addPoiGroup(poiType = ZOO, parentPoiGroups = ZOO.parentGroups, items = dto.zoos.items, distance = dto.zoos.searchRadius)
    }

    private fun mapStoT(dto: GeoLocalizationPointsOfInterestDto, poiGroups: MutableList<POIGroup>) {
        poiGroups += addPoiGroup(poiType = SCHOOL, parentPoiGroups = SCHOOL.parentGroups, items = dto.schools.items, distance = dto.schools.searchRadius)
        poiGroups += addPoiGroup(poiType = SUPERMARKET, parentPoiGroups = SUPERMARKET.parentGroups, items = dto.supermarkets.items, distance = dto.supermarkets.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = SAFETY_AMENITY, parentPoiGroups = SAFETY_AMENITY.parentGroups, items = dto.safetyAmenities.items, distance = dto.safetyAmenities.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = SHOPPING_CENTER, parentPoiGroups = SHOPPING_CENTER.parentGroups, items = dto.shoppingCenters.items, distance = dto.shoppingCenters.searchRadius)
        poiGroups += addPoiGroup(poiType = MALL, parentPoiGroups = MALL.parentGroups, items = dto.shoppingMalls.items, distance = dto.shoppingMalls.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = SPORT_BASKETBALL, parentPoiGroups = SPORT_BASKETBALL.parentGroups, items = dto.sportBasketball.items, distance = dto.sportBasketball.searchRadius)
        poiGroups += addPoiGroup(poiType = SPORT_GOLF_COURSE, parentPoiGroups = SPORT_GOLF_COURSE.parentGroups, items = dto.sportGolfCourses.items, distance = dto.sportGolfCourses.searchRadius)
        poiGroups += addPoiGroup(poiType = SPORT_HORSE_RIDING, parentPoiGroups = SPORT_HORSE_RIDING.parentGroups, items = dto.sportHorseRidings.items, distance = dto.sportHorseRidings.searchRadius)
        poiGroups += addPoiGroup(poiType = SPORT_ICE_RINK, parentPoiGroups = SPORT_ICE_RINK.parentGroups, items = dto.sportIceRinks.items, distance = dto.sportIceRinks.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = SPORT_PADDLE_SPORTS, parentPoiGroups = SPORT_PADDLE_SPORTS.parentGroups, items = dto.sportPaddleBoat.items, distance = dto.sportPaddleBoat.searchRadius)
        poiGroups += addPoiGroup(poiType = SPORT_SHOOTING, parentPoiGroups = SPORT_SHOOTING.parentGroups, items = dto.sportShooting.items, distance = dto.sportShooting.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = SPORT_SOCCER, parentPoiGroups = SPORT_SOCCER.parentGroups, items = dto.sportSoccer.items, distance = dto.sportSoccer.searchRadius)
        poiGroups += addPoiGroup(poiType = SPORT_TENNIS, parentPoiGroups = SPORT_TENNIS.parentGroups, items = dto.sportTennis.items, distance = dto.sportTennis.searchRadius)
        poiGroups += addPoiGroup(poiType = SPORT_SWIMMING, parentPoiGroups = SPORT_SWIMMING.parentGroups, items = dto.sportSwimming.items, distance = dto.sportSwimming.searchRadius)
        poiGroups += addPoiGroup(poiType = SPORT_WINTER, parentPoiGroups = SPORT_WINTER.parentGroups, items = dto.sportWinter.items, distance = dto.sportWinter.searchRadius)
        poiGroups += addPoiGroup(poiType = TRAIN_STATION, parentPoiGroups = TRAIN_STATION.parentGroups, items = dto.railwayStops.items, distance = dto.railwayStops.searchRadius)
    }

    private fun mapNtoR(dto: GeoLocalizationPointsOfInterestDto, poiGroups: MutableList<POIGroup>) {
        poiGroups += addPoiGroup(poiType = NATURE_RESERVE, parentPoiGroups = NATURE_RESERVE.parentGroups, items = dto.natureReserves.items, distance = dto.natureReserves.searchRadius)
        poiGroups += addPoiGroup(poiType = NIGHTCLUB, parentPoiGroups = NIGHTCLUB.parentGroups, items = dto.nightclubs.items, distance = dto.nightclubs.searchRadius)
        poiGroups += addPoiGroup(poiType = PARCEL_LOCKER, parentPoiGroups = PARCEL_LOCKER.parentGroups, items = dto.parcelLockers.items, distance = dto.parcelLockers.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = PARK, parentPoiGroups = PARK.parentGroups, items = dto.parks.items, distance = dto.parks.searchRadius)
        poiGroups += addPoiGroup(poiType = PHARMACY, parentPoiGroups = PHARMACY.parentGroups, items = dto.pharmacies.items, distance = dto.pharmacies.searchRadius)
        poiGroups += addPoiGroup(poiType = PLACE_OF_WORSHIP, parentPoiGroups = PLACE_OF_WORSHIP.parentGroups, items = dto.placesOfWorship.items, distance = dto.placesOfWorship.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = PLAYGROUND, parentPoiGroups = PLAYGROUND.parentGroups, items = dto.playgrounds.items, distance = dto.playgrounds.searchRadius)
        poiGroups += addPoiGroup(poiType = RESTAURANT_CAFE, parentPoiGroups = RESTAURANT_CAFE.parentGroups, items = dto.restaurantsCafes.items, distance = dto.restaurantsCafes.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = RIVER, parentPoiGroups = RIVER.parentGroups, items = dto.rivers.items, distance = dto.rivers.searchRadius)
    }

    private fun mapItoM(dto: GeoLocalizationPointsOfInterestDto, poiGroups: MutableList<POIGroup>) {
        poiGroups += addPoiGroupOptionalName(poiType = KIOSK, parentPoiGroups = KIOSK.parentGroups, items = dto.kiosks.items, distance = dto.kiosks.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = LAKE, parentPoiGroups = LAKE.parentGroups, items = dto.lakes.items, distance = dto.lakes.searchRadius)
        poiGroups += addPoiGroup(poiType = LIBRARY, parentPoiGroups = LIBRARY.parentGroups, items = dto.libraries.items, distance = dto.libraries.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = MARKETPLACE, parentPoiGroups = MARKETPLACE.parentGroups, items = dto.marketplaces.items, distance = dto.marketplaces.searchRadius)
        poiGroups += addPoiGroup(poiType = METROPOLITAN_CITY, parentPoiGroups = METROPOLITAN_CITY.parentGroups, items = dto.metropolitanCity.items, distance = dto.metropolitanCity.searchRadius)
        poiGroups += addPoiGroup(poiType = MOUNTAIN_RANGE, parentPoiGroups = MOUNTAIN_RANGE.parentGroups, items = dto.mountainRanges.items, distance = dto.mountainRanges.searchRadius)
        poiGroups += addPoiGroup(poiType = MUSIC_SCHOOL, parentPoiGroups = MUSIC_SCHOOL.parentGroups, items = dto.musicSchools.items, distance = dto.musicSchools.searchRadius)
    }

    private fun mapEtoH(dto: GeoLocalizationPointsOfInterestDto, poiGroups: MutableList<POIGroup>) {
        poiGroups += addPoiGroupOptionalName(poiType = ELECTRIC_CAR_CHARGER, parentPoiGroups = ELECTRIC_CAR_CHARGER.parentGroups, items = dto.electricCarChargers.items, distance = dto.electricCarChargers.searchRadius)
        poiGroups += addPoiGroup(poiType = FITNESS_CENTER, parentPoiGroups = FITNESS_CENTER.parentGroups, items = dto.fitnessCenters.items, distance = dto.fitnessCenters.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = FOREST, parentPoiGroups = FOREST.parentGroups, items = dto.woodlandAndForests.items, distance = dto.woodlandAndForests.searchRadius)
        poiGroups += addPoiGroup(poiType = FURNITURE_AND_INTERIOR, parentPoiGroups = FURNITURE_AND_INTERIOR.parentGroups, items = dto.furnitureInterior.items, distance = dto.furnitureInterior.searchRadius)
        poiGroups += addPoiGroup(poiType = GARDEN_CENTER, parentPoiGroups = GARDEN_CENTER.parentGroups, items = dto.gardenCenters.items, distance = dto.gardenCenters.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = HAIR_DRESSER, parentPoiGroups = HAIR_DRESSER.parentGroups, items = dto.hairDressers.items, distance = dto.hairDressers.searchRadius)
        poiGroups += addPoiGroup(poiType = HARDWARE_STORE, parentPoiGroups = HARDWARE_STORE.parentGroups, items = dto.hardwareStores.items, distance = dto.hardwareStores.searchRadius)
        poiGroups += addPoiGroup(poiType = HOSPITAL, parentPoiGroups = HOSPITAL.parentGroups, items = dto.hospitals.items, distance = dto.hospitals.searchRadius)
    }

    private fun mapCtoD(dto: GeoLocalizationPointsOfInterestDto, poiGroups: MutableList<POIGroup>) {
        poiGroups += addPoiGroup(poiType = CHEMIST, parentPoiGroups = CHEMIST.parentGroups, items = dto.chemists.items, distance = dto.chemists.searchRadius)
        poiGroups += addPoiGroup(poiType = CHILDCARE, parentPoiGroups = CHILDCARE.parentGroups, items = dto.childcare.items, distance = dto.childcare.searchRadius)
        poiGroups += addPoiGroup(poiType = CINEMA, parentPoiGroups = CINEMA.parentGroups, items = dto.cinemas.items, distance = dto.cinemas.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = COASTLINE, parentPoiGroups = COASTLINE.parentGroups, items = dto.coastlines.items, distance = dto.coastlines.searchRadius)
        poiGroups += addPoiGroup(poiType = DOCTORS, parentPoiGroups = DOCTORS.parentGroups, items = dto.doctors.items, distance = dto.doctors.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = DOG_PARK, parentPoiGroups = DOG_PARK.parentGroups, items = dto.dogParks.items, distance = dto.dogParks.searchRadius)
    }

    private fun mapAtoB(dto: GeoLocalizationPointsOfInterestDto, poiGroups: MutableList<POIGroup>) {
        poiGroups += addPoiGroup(poiType = AQUARIUM, parentPoiGroups = AQUARIUM.parentGroups, items = dto.aquariums.items, distance = dto.aquariums.searchRadius)
        poiGroups += addPoiGroup(poiType = INTERNATIONAL_AIRPORT, parentPoiGroups = INTERNATIONAL_AIRPORT.parentGroups, items = dto.airports.items, distance = dto.airports.searchRadius)
        poiGroups += addPoiGroup(poiType = AMUSEMENT_PARK, parentPoiGroups = AMUSEMENT_PARK.parentGroups, items = dto.amusementParks.items, distance = dto.amusementParks.searchRadius)
        poiGroups += addPoiGroup(poiType = ANIMAL_TRAINING, parentPoiGroups = ANIMAL_TRAINING.parentGroups, items = dto.animalTrainings.items, distance = dto.animalTrainings.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = BAKERY, parentPoiGroups = BAKERY.parentGroups, items = dto.bakeries.items, distance = dto.bakeries.searchRadius)
        poiGroups += addPoiGroup(poiType = BANK, parentPoiGroups = BANK.parentGroups, items = dto.banks.items, distance = dto.banks.searchRadius)
        poiGroups += addPoiGroup(poiType = BAR_PUB, parentPoiGroups = BAR_PUB.parentGroups, items = dto.barsPubs.items, distance = dto.barsPubs.searchRadius)
        poiGroups += addPoiGroupOptionalName(poiType = BATHING_PLACE, parentPoiGroups = BATHING_PLACE.parentGroups, items = dto.bathingPlaces.items, distance = dto.bathingPlaces.searchRadius)
        poiGroups += addPoiGroup(poiType = BUS_STOP, parentPoiGroups = BUS_STOP.parentGroups, items = dto.busStops.items, distance = dto.busStops.searchRadius)
        poiGroups += addPoiGroup(poiType = BOWLING_ALLEY, parentPoiGroups = BOWLING_ALLEY.parentGroups, items = dto.bowlingAlleys.items, distance = dto.bowlingAlleys.searchRadius)
    }


    private fun addPoiGroupOptionalName(poiType: PoiType, parentPoiGroups: List<PoiParentGroupType>, items: List<PoiDto>, distance: Int): POIGroup {
        return POIGroup(
            radiusInMeter = distance,
            poiType = poiType,
            poiList = mapToPoiListNameOptional(items),
            parentGroups = parentPoiGroups
        )
    }

    private fun addPoiGroup(poiType: PoiType, parentPoiGroups: List<PoiParentGroupType>, items: List<PoiWithNameRequiredDto>, distance: Int): POIGroup {
        return POIGroup(
            radiusInMeter = distance,
            poiType = poiType,
            poiList = mapToPoiList(items),
            parentGroups = parentPoiGroups
        )
    }

    private fun mapToPoiListNameOptional(items: List<PoiDto>): List<POI> {
        return items.map {
            POI(
                name = it.name,
                distanceInMeters = it.distanceM,
                latitude = it.position.latitude,
                longitude = it.position.longitude,
                details = it.details?.associate { detail -> detail.key to detail.value }
            )
        }
    }

    private fun mapToPoiList(items: List<PoiWithNameRequiredDto>): List<POI> {
        return items.map {
            POI(
                name = it.name,
                distanceInMeters = it.distanceM,
                latitude = it.position.latitude,
                longitude = it.position.longitude,
                details = it.details?.associate { detail -> detail.key to detail.value }
            )
        }
    }

}
