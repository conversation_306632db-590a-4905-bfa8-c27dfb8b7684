package com.doorbit.bff.infra.listing.applicationservice

import com.doorbit.bff.infra.listing.model.listing.Listing
import com.doorbit.bff.infra.listing.model.listing.building.ConstructionPartType.*
import com.doorbit.bff.infra.listing.model.listing.building.ListingFieldHelper.createFieldDataInner
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec
import org.springframework.stereotype.Service

@Service
class FieldsSetService(
    private val buildingService: BuildingService
) {

    /**
     * Do something when a field was set
     */
    fun onFieldsSet(listing: Listing, updateResult: ListingFieldUpdateResult) {
        if (!updateResult.hasChanged()) return

        uValueUpdates(updateResult, listing)


    }

    private fun uValueUpdates(
        updateResult: ListingFieldUpdateResult,
        listing: Listing
    ) {
        if (updateResult.isFieldChanged(FieldSpec.WallUValueField.fieldName)) {
            val uValue = listing.fields[FieldSpec.WallUValueField]
            buildingService.setBuildingCustomData(
                listing,
                WALL,
                createFieldDataInner(FieldSpec.WindowUValueField.fieldName, uValue)
            )
        }

        if (updateResult.isFieldChanged(FieldSpec.GroundSlabUValueField.fieldName)) {
            val slabs = listing.building!!.groundPlateSlabConstructionParts()
            val uValue = listing.fields[FieldSpec.GroundSlabUValueField]
            slabs.forEach {
                buildingService.setBuildingCustomData(
                    listing,
                    it,
                    createFieldDataInner(FieldSpec.GroundSlabUValueField.fieldName, uValue)
                )
            }
        }

        if (updateResult.isFieldChanged(FieldSpec.TopLevelCeilingUValueField.fieldName)) {
            val slabs = listing.building!!.topLevelPlateSlabConstructionParts()
            val uValue = listing.fields[FieldSpec.TopLevelCeilingUValueField]
            slabs.forEach {
                buildingService.setBuildingCustomData(
                    listing,
                    it,
                    createFieldDataInner(FieldSpec.TopLevelCeilingUValueField.fieldName, uValue)
                )
            }
        }

        if (updateResult.isFieldChanged(FieldSpec.WindowUValueField.fieldName)) {
            val uValue = listing.fields[FieldSpec.WindowUValueField]
            buildingService.setBuildingCustomData(
                listing,
                WINDOW,
                createFieldDataInner(FieldSpec.WindowUValueField.fieldName, uValue)
            )
        }

        if (updateResult.isFieldChanged(FieldSpec.DoorUValueField.fieldName)) {
            val uValue = listing.fields[FieldSpec.DoorUValueField]
            buildingService.setBuildingCustomData(
                listing,
                DOOR,
                createFieldDataInner(FieldSpec.DoorUValueField.fieldName, uValue),
                outsidePartsOnly = true
            )
        }

        if (updateResult.isFieldChanged(FieldSpec.CellarCeilingUValueField.fieldName)) {
            val parts = listing.building!!.cellarSlabConstructionParts()
            val uValue = listing.fields[FieldSpec.CellarCeilingUValueField]
            parts.forEach {
                buildingService.setBuildingCustomData(
                    listing,
                    it,
                    createFieldDataInner(FieldSpec.CellarCeilingUValueField.fieldName, uValue)
                )
            }
        }
    }
}