import {ACESFilmicToneMapping, Color, DoubleSide, FrontSide, LineBasicMaterial, LineBasicMaterialParameters, MeshBasicMaterial, MeshBasicMaterialParameters, MeshStandardMaterial, MeshStandardMaterialParameters, Vector3} from "three";
import {TMaterial, TMaterialDeclaration} from "@/adapter/three/TMaterial";
import {TSelectionRaycasterEmitterOrConsumer, TSelectionRelated} from "@/adapter/three/raycasting/selection/TSelectionRaycasterEmitterOrConsumer";
import {TOcclusionCullingRaycasterEmitterOrConsumer, TOcclusionCullingRelated} from "@/adapter/three/raycasting/occlusion-culling/TOcclusionCullingRaycasterEmitterOrConsumer";
import {THoverRaycasterEmitterOrConsumer, THoverRelated} from "@/adapter/three/raycasting/hover/THoverRaycasterEmitterOrConsumer";
import {BUILDING_EPSILON, floorTypeOfFloor, noThicknessOpeningOffsetZ, noThicknessWallOffsetZ} from "@/components/listing/building/building";
import {RenderVisibility} from "@/components/listing/building/renderer/RenderVisibility";
import {Optional} from "@/model/Optional";
import {TMaybeRaycasterRelated} from "@/adapter/three/raycasting/TRaycasterRelated";
import {GTAOEffect} from "@/adapter/three/postprocessing/GTAOEffect";
import {BuildingRenderer, OpeningRelocationMode} from "@/components/listing/building/renderer/BuildingRenderer";
import {BuildingComponent, FloorSlabCeilingBuildingComponent, FloorSlabFloorBuildingComponent, RaycastableBuildingComponent, WallRoofPoint, WallSizeAdjustmentBuildingComponent, WallWithHolesBuildingComponent, WallWithoutHolesBuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
import {Building, ConstructionPart} from "@/adapter/graphql/generated/graphql";
import {TDraggableRaycasterEmitterOrConsumer, TDraggableRelated} from "@/adapter/three/raycasting/draggable/TDraggableRaycasterEmitterOrConsumer";
import {TRaycaster} from "@/adapter/three/raycasting/TRaycaster";
import {Ref, toRaw} from "vue";
import {HistoryManager} from "@/utility/history-manager";
import {wallTypeOfWall} from "@/model/building/WallType";
import {WallOpeningCreator} from "@/components/listing/building/renderer/WallOpeningCreator";
import {ABSPERRBARKE_SHADER_MATERIAL} from "@/components/listing/building/renderer/shader/AbsperrbarkeShaderMaterial";
import {TraversalBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";
import {ORTHOGONALE_LINIEN_SHADER_MATERIAL} from "@/components/listing/building/renderer/shader/OrthogonaleLinienShaderMaterial";
import {RoofAreaCreator} from "@/components/listing/building/renderer/RoofAreaCreator";
// --- ROOF AREA ---

// #################
// ### MATERIALS ###
// #################
// --- DEBUG ---
const MATERIAL_DEBUG = new MeshBasicMaterial({
    color: 0xFF00FF,
    side: DoubleSide,
    transparent: true,
    opacity: 0.5,
    depthTest: false,
})

// --- BUILDING ---
const MATERIAL_BUILDING = new MeshBasicMaterial({
    color: 0xFF0000,
    side: FrontSide,
    wireframe: true,
    transparent: true,
    depthTest: false,
})

// --- FLOOR ---
const MATERIAL_FLOOR = new MeshBasicMaterial({
    color: 0x00FFFF,
    side: FrontSide,
    wireframe: true,
    transparent: true,
    depthTest: false,
})

// --- FLOOR SLAB (FLOOR / CEILING) ---
const MATERIAL_PARAM_FLOOR_SLAB: MeshBasicMaterialParameters = {
    side: FrontSide,
}
const MATERIAL_FLOOR_SLAB_DEFAULT = new MeshBasicMaterial({
    ...MATERIAL_PARAM_FLOOR_SLAB,
    color: 0x9F9F9F,
})
const MATERIAL_FLOOR_SLAB_BASEMENT = new MeshBasicMaterial({
    ...MATERIAL_PARAM_FLOOR_SLAB,
    color: 0x343951,
})

const MATERIAL_ROOF_AREA = ORTHOGONALE_LINIEN_SHADER_MATERIAL.clone()
MATERIAL_ROOF_AREA.uniforms.backgroundColor.value = new Color(0x5A3A24)
MATERIAL_ROOF_AREA.uniforms.lineColor.value = new Color(0x1F2230)
MATERIAL_ROOF_AREA.uniforms.horizontalSpacing.value = 0.03
MATERIAL_ROOF_AREA.uniforms.verticalSpacing.value = 0.06
MATERIAL_ROOF_AREA.uniforms.horizontalThickness.value = 0.003
MATERIAL_ROOF_AREA.uniforms.verticalThickness.value = 0.005
MATERIAL_ROOF_AREA.side = FrontSide

const MATERIAL_ROOF_AREA_TEMP = ORTHOGONALE_LINIEN_SHADER_MATERIAL.clone()
MATERIAL_ROOF_AREA_TEMP.uniforms.backgroundColor.value = new Color(0x000000)
MATERIAL_ROOF_AREA_TEMP.uniforms.lineColor.value = new Color(0xFFCF2E)
MATERIAL_ROOF_AREA_TEMP.uniforms.horizontalSpacing.value = 0.03
MATERIAL_ROOF_AREA_TEMP.uniforms.verticalSpacing.value = 0.06
MATERIAL_ROOF_AREA_TEMP.uniforms.horizontalThickness.value = 0.003
MATERIAL_ROOF_AREA_TEMP.uniforms.verticalThickness.value = 0.005
MATERIAL_ROOF_AREA_TEMP.side = FrontSide

// --- ROOM ---
const MATERIAL_ROOM = new MeshBasicMaterial({
    color: 0x0000FF,
    side: FrontSide,
    wireframe: true,
    transparent: true,
    depthTest: false,
})
const MATERIAL_ROOM_UNRECOGNIZED = new MeshBasicMaterial({
    color: 0x000000,
    side: FrontSide,
    transparent: true,
    depthTest: false,
    opacity: 1,
    wireframe: true
})
// --- ROOM SLAB FLOOR ---
const MATERIAL_ROOM_FLOOR_SLAB_FLOOR: MeshBasicMaterialParameters = {
    side: FrontSide,
}
const MATERIAL_ROOM_SLAB_FLOOR_DEFAULT = new MeshBasicMaterial({
    ...MATERIAL_ROOM_FLOOR_SLAB_FLOOR,
    color: 0xC4C9D6,
})
const MATERIAL_ROOM_SLAB_FLOOR_BASEMENT = new MeshBasicMaterial({
    ...MATERIAL_ROOM_FLOOR_SLAB_FLOOR,
    color: 0x444A69,
})
// --- ROOM SLAB FLOOR SEGMENT ---
const MATERIAL_ROOM_SLAB_FLOOR_SEGMENT = new MeshBasicMaterial({
    side: FrontSide,
    color: 0xFF0000,
    depthTest: false,
    transparent: true,
    opacity: 0.5,
})
// --- ROOM SLAB CEILING ---
const MATERIAL_ROOM_SLAB_CEILING = new MeshBasicMaterial({
    side: FrontSide,
    color: 0xFFFF00,
    depthTest: false,
    transparent: true,
    wireframe: true,
})
// --- ROOM SLAB CEILING SEGMENT ---
const MATERIAL_ROOM_SLAB_CEILING_SEGMENT = new MeshBasicMaterial({
    side: FrontSide,
    color: 0x0000FF,
    depthTest: false,
    transparent: true,
    opacity: 0.5,
})

// --- ROOM TEXT BACKGROUND ---
const MATERIAL_ROOM_TEXT_BACKGROUND = new MeshBasicMaterial({
    color: 0x2A2E3F,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})
// --- ROOM TEXT ---
const MATERIAL_ROOM_TEXT = new MeshBasicMaterial({
    color: 0xFEFEFE,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})

// --- DOOR ---
const MATERIAL_DOOR = new MeshBasicMaterial({
    color: 0x009F64,
    side: FrontSide,
})
const MATERIAL_DOORWAY_LINES = new LineBasicMaterial({
    color: 0x009F64,
    side: FrontSide,
    linewidth: 0.03,
})

// --- WINDOW ---
const MATERIAL_WINDOW = new MeshBasicMaterial({
    color: 0x0095D0,
    opacity: 0.75,
    transparent: true,
})

// --- POINT OF INTEREST
const MATERIAL_POINT_OF_INTEREST = new MeshBasicMaterial({
    color: 0x00FFFF,
    transparent: true,
    depthTest: false,
    opacity: 0.5
})

// --- POINT OF INTEREST CAMERA
const MATERIAL_POINT_OF_INTEREST_CAMERA = new MeshBasicMaterial({
    color: 0x000000,
    depthTest: false,
    transparent: true,
})

// --- WALL ---
const MATERIAL_PARAM_WALL: MeshStandardMaterialParameters = {
    roughness: 0.5,
    metalness: 0,
    side: FrontSide,
}
const MATERIAL_PARAM_WALL_INTERIOR: MeshStandardMaterialParameters = {
    ...MATERIAL_PARAM_WALL,
    color: 0xFFFFFF,
}
const MATERIAL_PARAM_WALL_EXTERIOR: MeshStandardMaterialParameters = {
    ...MATERIAL_PARAM_WALL,
    color: 0xC4C9D6,
}
const MATERIAL_PARAM_WALL_INTERMEDIATE: MeshStandardMaterialParameters = {
    ...MATERIAL_PARAM_WALL,
    color: 0x848BA5,
}
const MATERIAL_PARAM_WALL_BASEMENT: MeshStandardMaterialParameters = {
    ...MATERIAL_PARAM_WALL,
    color: 0x444A69,
}
const MATERIAL_PARAM_WALL_NO_CONNECTION: MeshStandardMaterialParameters = {
    ...MATERIAL_PARAM_WALL,
    color: 0xf45446,
}
const MATERIAL_WALL_INTERIOR = new MeshStandardMaterial({
    ...MATERIAL_PARAM_WALL_INTERIOR,
})
const MATERIAL_WALL_EXTERIOR = new MeshStandardMaterial({
    ...MATERIAL_PARAM_WALL_EXTERIOR,
})
const MATERIAL_WALL_INTERMEDIATE = new MeshStandardMaterial({
    ...MATERIAL_PARAM_WALL_INTERMEDIATE,
})
const MATERIAL_WALL_BASEMENT = new MeshStandardMaterial({
    ...MATERIAL_PARAM_WALL_BASEMENT,
})
const MATERIAL_WALL_NO_CONNECTION = new MeshStandardMaterial({
    ...MATERIAL_PARAM_WALL_NO_CONNECTION,
})
const MATERIAL_WALL_INTERIOR_OCCL_CULLED = new MeshStandardMaterial({
    ...MATERIAL_PARAM_WALL_INTERIOR,
    transparent: true,
    opacity: 0.5,
})
const MATERIAL_WALL_EXTERIOR_OCCL_CULLED = new MeshStandardMaterial({
    ...MATERIAL_PARAM_WALL_EXTERIOR,
    transparent: true,
    opacity: 0.5,
})
const MATERIAL_WALL_INTERMEDIATE_OCCL_CULLED = new MeshStandardMaterial({
    ...MATERIAL_PARAM_WALL_INTERMEDIATE,
    transparent: true,
    opacity: 0.5,
})
const MATERIAL_WALL_BASEMENT_OCCL_CULLED = new MeshStandardMaterial({
    ...MATERIAL_PARAM_WALL_BASEMENT,
    transparent: true,
    opacity: 0.5,
})
const MATERIAL_WALL_NO_CONNECTION_OCCL_CULLED = new MeshStandardMaterial({
    ...MATERIAL_PARAM_WALL_NO_CONNECTION,
    transparent: true,
    opacity: 0.5,
})

// --- WALL SIZE ADJUSTMENT ---
const MATERIAL_WALL_SIZE_ADJUSTMENT = ABSPERRBARKE_SHADER_MATERIAL.clone()
MATERIAL_WALL_SIZE_ADJUSTMENT.uniforms.uStripeSpacing.value = 1
MATERIAL_WALL_SIZE_ADJUSTMENT.uniforms.uAlpha.value = 0.015
MATERIAL_WALL_SIZE_ADJUSTMENT.uniforms.uColor.value = new Color(0x000000)
MATERIAL_WALL_SIZE_ADJUSTMENT.uniforms.uBackgroundColor.value = new Color(0xFFFFFF)
MATERIAL_WALL_SIZE_ADJUSTMENT.side = FrontSide
MATERIAL_WALL_SIZE_ADJUSTMENT.transparent = true

// --- WALL ROOF POINT ---
const MATERIAL_WALL_ROOF_POINT = new MeshBasicMaterial({
    color: 0x43E77F,
    side: FrontSide,
    transparent: true,
    depthTest: false,
})

// --- WALL METRICS ---
const MATERIAL_PARAM_METRICS_LINE: LineBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    linewidth: 0.02,
    transparent: true,
}
const MATERIAL_METRICS_LINE = new LineBasicMaterial({
    ...MATERIAL_PARAM_METRICS_LINE,
    color: 0xFF0000,
})
const MATERIAL_METRICS_LINE_NOT_FOCUSED = new LineBasicMaterial({
    ...MATERIAL_PARAM_METRICS_LINE,
    color: 0xFFA56B,
})
const MATERIAL_PARAM_METRICS_TEXT_BACKGROUND: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT_BACKGROUND = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_BACKGROUND,
    color: 0xFF0000,
})
const MATERIAL_METRICS_TEXT_BACKGROUND_NOT_FOCUSED = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_BACKGROUND,
    color: 0xFFA56B,
})
const MATERIAL_PARAM_METRICS_TEXT: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT,
    color: 0xFFFFFF,
})
const MATERIAL_METRICS_TEXT_NOT_FOCUSED = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT,
    color: 0x000000,
})
const MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID_BACKGROUND: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT_DISPLAY_ID_BACKGROUND = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID_BACKGROUND,
    color: 0x005D3A,
})
const MATERIAL_METRICS_TEXT_DISPLAY_ID_BACKGROUND_NOT_FOCUSED = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID_BACKGROUND,
    color: 0x43E77F,
})
const MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT_DISPLAY_ID = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID,
    color: 0xFFFFFF,
})
const MATERIAL_METRICS_TEXT_DISPLAY_ID_NOT_FOCUSED = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID,
    color: 0x000000,
})
const MATERIAL_METRICS_TEXT_THICKNESS_BACKGROUND = new MeshBasicMaterial({
    color: 0xFFFFFF,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})
const MATERIAL_METRICS_TEXT_THICKNESS = new MeshBasicMaterial({
    color: 0x000000,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})

// --- FURNITURE ---
const MATERIAL_FURNITURE = new MeshStandardMaterial({
    color: 0x0095D0,
    roughness: 0.6,
    metalness: 0,
    side: FrontSide,
})

// --- SELECTION ---
const MATERIAL_SELECTION = new MeshBasicMaterial({
    color: 0xFFCF2E,
    side: FrontSide,
    transparent: true,
    depthTest: false,
    opacity: 0.5
})
const MATERIAL_SUBSELECTION = new MeshBasicMaterial({
    color: 0xFF7F54,
    side: FrontSide,
    transparent: true,
    depthTest: false,
    opacity: 0.75
})

// --- HOVER ---
const MATERIAL_HOVER = new MeshBasicMaterial({
    color: 0xFFFFFF,
    side: FrontSide,
    transparent: true,
    depthTest: false,
    opacity: 0.5
})

// --- SELECTION + HOVER ---
const MATERIAL_SELECTION_AND_HOVER = new MeshBasicMaterial({
    color: 0xF9DE74,
    side: FrontSide,
    transparent: true,
    depthTest: false,
    opacity: 0.5
})

// --- FLOOR GRID ---
const MATERIAL_FLOOR_GRID = new LineBasicMaterial({
    color: 0x000000,
    side: FrontSide,
    depthTest: false,
    transparent: true,
    opacity: 0.5,
})

// #######################
// ### RENDERER CONFIG ###
// #######################
export class BuildingRenderer3D extends BuildingRenderer {
    showGrid = false
    showPoles = false
    showAxis = false
    debugOcclusionCulling = false
    debugUnrecognizedRooms = false
    debugHoles = false

    /**
     * @param listingId
     * @param building Muss mutable und voll reaktiv sein.
     * @param canEdit
     * @param previousHistoryManager
     */
    constructor(
        listingId: string,
        building: Readonly<Ref<Building>>,
        canEdit: boolean,
        previousHistoryManager: Optional<HistoryManager<Building>>,
    ) {
        super(
            "3D",
            listingId,
            building,
            canEdit,
            "3D",
            ACESFilmicToneMapping,
            [
                new GTAOEffect(4),
                //new FXAAEffect()
            ],
            0.01, //in meters (1 cm)
            previousHistoryManager,
            "WEBGL"
        )

        this.draggableRaycaster?.destroy()
        //this.snappingManager?.destroy()
        this.wallCreator?.destroy()
    }

    destroy() {
        super.destroy();

        for (const effect of this.effects) {
            effect.destroy();
        }
    }

    // noinspection OverlyComplexFunctionJS
    isSelectable(component: BuildingComponent & TSelectionRelated<TSelectionRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>>): boolean {
        const mode = this.mode.value;
        if (mode === "OPENING_CREATION") {
            return false
        }
        if (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING") {
            return false
        }
        if (mode === "POI_ADDING") {
            return false
        }
        if (mode === "ROOF_AREA_CREATION") {
            return component.type === "WALL_ROOF_POINT" && this.isWallRoofPointValid(component.roofPoint)
        }
        if (component.type === "WALL_WITHOUT_HOLES") {
            return false
        }
        if (component.type === "FURNITURE") {
            return this.showFurniture.value
        }
        // noinspection RedundantIfStatementJS
        if (component.type === "UNRECOGNIZED_ROOM") {
            return false
        }
        if (component.type === "ROOM_SLAB_FLOOR") {
            return this.isComponentVisibleRelatedToFloor(component)
        }
        if (component.type === "POINT_OF_INTEREST") {
            return this.showPointsOfInterest.value
        }
        if (component.type === "WALL_OPENING_DOOR" || component.type === "WALL_OPENING_WINDOW" || component.type === "WALL_OPENING_OPENING") {
            return !this.isEvebiModeEnabled.value
        }
        if (component.type === "ROOF_AREA") {
            return this.showRoofAreas.value
        }
        return true
    }

    isOcclusionCullable(component: BuildingComponent & TOcclusionCullingRelated<TOcclusionCullingRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent>>>): boolean {
        if (this.mode.value === "POI_ADDING") {
            return false
        }
        if (component.type === "FURNITURE") {
            return this.showFurniture.value
        }
        return true;
    }

    // noinspection OverlyComplexFunctionJS
    isHoverable(component: BuildingComponent & THoverRelated<THoverRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>>): boolean {
        const mode = this.mode.value;
        if (
            mode === "POI_ADDING" &&
            component.type !== "WALL_WITH_HOLES" &&
            component.type !== "WALL_OPENING_WINDOW" &&
            component.type !== "WALL_OPENING_OPENING" &&
            component.type !== "WALL_OPENING_DOOR" &&
            component.type !== "ROOM_SLAB_FLOOR"
        ) {
            return false
        }

        const isOpeningCreationMode = mode === "OPENING_CREATION"
        const isRelocateOpeningMode = typeof mode !== 'string' && mode.type === "RELOCATE_OPENING"
        const isAnyOpeningMode = isOpeningCreationMode || isRelocateOpeningMode

        if (mode === "ROOF_AREA_CREATION") {
            return component.type === "WALL_ROOF_POINT" && this.isWallRoofPointValid(component.roofPoint)
        }
        if (isAnyOpeningMode && component.type !== "WALL_WITHOUT_HOLES") {
            return false
        }
        if (component.type === "WALL_WITHOUT_HOLES") {
            if (isRelocateOpeningMode) {
                return (mode as OpeningRelocationMode).wall.id === component.component.value.id
            }
            return isOpeningCreationMode && component.component.value.shapeRepresentation.shape.__typename !== "Ring" //TODO: WallCreation für Ring-Wände ermöglichen
        }
        if (component.type === "FURNITURE") {
            return this.showFurniture.value
        }
        if (component.type === "POINT_OF_INTEREST") {
            return this.showPointsOfInterest.value
        }
        // noinspection RedundantIfStatementJS
        if (component.type === "UNRECOGNIZED_ROOM") {
            return false
        }
        if (component.type === "ROOM_SLAB_FLOOR") {
            return this.isComponentVisibleRelatedToFloor(component)
        }
        if (component.type === "WALL_OPENING_DOOR" || component.type === "WALL_OPENING_WINDOW" || component.type === "WALL_OPENING_OPENING") {
            return !this.isEvebiModeEnabled.value
        }
        if (component.type === "ROOF_AREA") {
            return this.showRoofAreas.value
        }
        return true
    }

    calculateIsCameraFitIgnored(component: BuildingComponent): boolean {
        return component.type !== "BUILDING"
    }

    calculateDebug(component: BuildingComponent): boolean {
        return false
    }

    // noinspection OverlyComplexFunctionJS,FunctionTooLongJS
    calculateVisibility(component: BuildingComponent): RenderVisibility {
        //BUILDING
        if (component.type === "BUILDING") {
            return "SELF_HIDDEN_CHILDREN_VISIBLE"
        }

        //FLOORS
        if (component.type === "FLOOR") {
            if (!this.visibleFloorLevels.value.has(component.component.value.level)) {
                return "HIDDEN"
            }
            return this.isComponentHovered(component) || this.isComponentSelected(component) ? "VISIBLE" : "SELF_HIDDEN_CHILDREN_VISIBLE"
        }
        if (component.type === "FLOOR_SLAB_FLOOR") {
            return this.showSlabs.value ? "VISIBLE" : "HIDDEN"
        }
        if (component.type === "FLOOR_SLAB_CEILING") {
            if (!this.showSlabs.value) {
                return "HIDDEN"
            }
            return this.isComponentHovered(component.floorComponent) || this.isComponentSelected(component.floorComponent) ? "VISIBLE" : "HIDDEN"
        }

        //WALL ROOF POINT
        if (component.type === "WALL_ROOF_POINT") {
            return this.isWallRoofPointValid(component.roofPoint) ? "VISIBLE" : "HIDDEN"
        }

        //ROOF AREAS
        const mode = this.mode.value;
        if (component.type === "ROOF_AREA") {
            if (mode === "ROOF_AREA_CREATION") {
                return "VISIBLE"
            }
            if (mode === "POI_ADDING") {
                return "HIDDEN"
            }
            if (mode === "OPENING_CREATION" || (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING")) {
                return "HIDDEN"
            }
            return this.showRoofAreas.value ? "VISIBLE" : "HIDDEN"
        }

        //ROOMS
        if (component.type === "ROOM") {
            return this.isComponentHovered(component) || this.isComponentSelected(component) ? "VISIBLE" : "SELF_HIDDEN_CHILDREN_VISIBLE"
        }
        if (component.type === "UNRECOGNIZED_ROOM") {
            return "ALWAYS_HIDDEN"
        }
        if (component.type === "ROOM_SLAB_FLOOR") {
            return this.showSlabs.value || this.isComponentHovered(component) || this.isComponentSelected(component) ? "VISIBLE" : "HIDDEN"
        }
        if (component.type === "ROOM_SLAB_FLOOR_SEGMENT") {
            return "ALWAYS_HIDDEN"
        }
        if (component.type === "ROOM_SLAB_CEILING") {
            return "ALWAYS_HIDDEN"
        }
        if (component.type === "ROOM_SLAB_CEILING_SEGMENT") {
            return "ALWAYS_HIDDEN"
        }

        // FLOOR GRID
        if (component.type === "FLOOR_GRID") {
            return this.showFloorGrid.value && this.isComponentSelected(component.floorComponent) ? "VISIBLE" : "HIDDEN"
        }

        //POINT OF INTEREST
        if (component.type === "POINT_OF_INTEREST") {
            if (mode === "ROOF_AREA_CREATION") {
                return "HIDDEN"
            }
            return this.showPointsOfInterest.value ? "VISIBLE" : "HIDDEN"
        }

        //POINT OF INTEREST CAMERA
        if (component.type === "POINT_OF_INTEREST_CAMERA") {
            if (!this.showPointsOfInterest.value) {
                return "HIDDEN"
            }
            return this.isComponentHovered(component.pointOfInterestComponent) || this.isComponentSelected(component.pointOfInterestComponent) ? "VISIBLE" : "HIDDEN"
        }

        //OPENING
        if (component.type === "WALL_OPENING_OPENING") {
            return this.isComponentHovered(component) || this.isComponentSelected(component) ? "VISIBLE" : "HIDDEN"
        }

        //FURNITURE
        if (component.type === "FURNITURE") {
            return this.showFurniture.value ? "VISIBLE" : "HIDDEN"
        }

        //WALL
        if (component.type === "WALL_WITH_HOLES") {
            if (mode === "ROOF_AREA_CREATION") {
                return "HIDDEN"
            }
            if (this.isEvebiModeEnabled.value) {
                return "HIDDEN"
            }
            if (this.isComponentHovered(component) || this.isComponentSelected(component)) {
                return "VISIBLE"
            }
            if (mode === "POI_ADDING" || mode === "OPENING_CREATION" || (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING")) {
                return "VISIBLE"
            }
            // noinspection NonBlockStatementBodyJS
            if (!this.visibleWallTypes.value.has(wallTypeOfWall(component.component.value))) {
                return "HIDDEN"
            }
            return "VISIBLE"
        }
        if (component.type === "WALL_WITHOUT_HOLES") {
            if (mode === "ROOF_AREA_CREATION") {
                return "VISIBLE"
            }
            return "HIDDEN"
        }
        if (component.type === "WALL_SNAP_POINT") {
            //snap points werden der scene (root) hinzugefügt und können nicht über den floor ausgeblendet werden
            return /*this.isComponentVisibleRelatedToFloor(component) ? "VISIBLE" : */"HIDDEN"
        }
        if (component.type === "WALL_SNAP_LINE") {
            return "HIDDEN"
        }

        //ROOM TEXT
        if (component.type === "ROOM_TEXT") {
            return this.showRoomTexts.value ? "VISIBLE" : "HIDDEN"
        }
        if (component.type === "ROOM_TEXT_BACKGROUND") {
            return this.visibility(component.textComponent) //recursion
        }

        //WALL METRICS
        if (component.type === "WALL_METRICS_TEXT_WIDTH" || component.type === "WALL_METRICS_TEXT_DISPLAY_ID" || component.type === "WALL_METRICS_TEXT_THICKNESS") {
            if (mode === "OPENING_CREATION" || (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING")) {
                return "HIDDEN"
            }
            if (mode === "POI_ADDING") {
                return "HIDDEN"
            }
            const text = component.customShapeRepresentation.text.value
            if (text === null || text.trim() === "") {
                return "HIDDEN"
            }
            if (component.type === "WALL_METRICS_TEXT_DISPLAY_ID") {
                if (this.showDisplayIds.value) {
                    return "VISIBLE"
                }
                if (this.isEvebiModeEnabled.value && this.displayIdSizeAdjustmentXEffectedWallIds.value.has(component.wallWithHolesComponent.component.value.id)) {
                    return "VISIBLE"
                }
            }
            if (component.type === "WALL_METRICS_TEXT_THICKNESS") {
                if (this.showWallThicknesses.value) {
                    return "VISIBLE"
                }
                if (this.isEvebiModeEnabled.value && this.displayIdSizeAdjustmentXEffectedWallIds.value.has(component.wallWithHolesComponent.component.value.id)) {
                    return "VISIBLE"
                }
            }
            if (component.type === "WALL_METRICS_TEXT_WIDTH" && this.showWallWidths.value) {
                return "VISIBLE"
            }
            if (this.isRoomOfWallHoveredOrSelected(component.wallWithHolesComponent)) {
                return "VISIBLE"
            }
            return this.isEffectedByAnyWallSnapPoint(component.wallWithHolesComponent, snapPointComponent =>
                this.isComponentHovered(snapPointComponent)
                || this.isComponentSelected(snapPointComponent)
            ) ? "VISIBLE" : "HIDDEN"
        }
        if (component.type === "WALL_METRICS_LINE" || component.type === "WALL_METRICS_TEXT_WIDTH_BACKGROUND" || component.type === "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND" || component.type === "WALL_METRICS_TEXT_THICKNESS_BACKGROUND") {
            return this.visibility(component.textComponent) //recursion
        }

        //WALL OPENING METRICS
        if (component.type === "WALL_OPENING_METRICS_TEXT") {
            if (mode === "POI_ADDING") {
                return "HIDDEN"
            }
            const text = component.customShapeRepresentation.text.value
            if (text === null || text.trim() === "") {
                return "HIDDEN"
            }
            if (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING") {
                return mode.opening.id === component.component.value.id ? "VISIBLE" : "HIDDEN"
            }
            if (mode === "OPENING_CREATION") {
                return component.component.value.id === WallOpeningCreator.TEMP_OPENING_ID ? "VISIBLE" : "HIDDEN"
            }
            return this.isComponentHovered(component.openingComponent) || this.isComponentSelected(component.openingComponent) ? "VISIBLE" : "HIDDEN"
        }
        if (component.type === "WALL_OPENING_METRICS_LINE" || component.type === "WALL_OPENING_METRICS_TEXT_BACKGROUND") {
            return this.visibility(component.textComponent) //recursion
        }

        return "VISIBLE"
    }

    // noinspection OverlyComplexFunctionJS,FunctionTooLongJS
    calculateMaterialOf(component: TMaybeRaycasterRelated<TraversalBuildingComponent, BuildingComponent>): TMaterialDeclaration<TMaterial> {
        const isHovered = (this.isComponentHovered(component) || (component.type === "WALL_SIZE_ADJUSTMENT" && this.isComponentHovered(component.wallComponent))) && (this.mode.value !== "POI_ADDING" || component.type === "WALL_OPENING_OPENING")
        const isSelected = this.isComponentSelected(component) || (component.type === "WALL_SIZE_ADJUSTMENT" && this.isComponentSelected(component.wallComponent))

        if (isSelected && isHovered) {
            return MATERIAL_SELECTION_AND_HOVER
        }
        if (isSelected) {
            return MATERIAL_SELECTION
        }
        if (isHovered) {
            return MATERIAL_HOVER
        }

        switch (component.type) {
            case "BUILDING":
                return MATERIAL_BUILDING
            case "FLOOR":
                return MATERIAL_FLOOR
            case "FLOOR_SLAB_FLOOR":
                return this.floorSlabMaterial(component)
            case "FLOOR_SLAB_CEILING":
                return this.floorSlabMaterial(component)
            case "FURNITURE":
                return MATERIAL_FURNITURE
            case "ROOM":
                return MATERIAL_ROOM
            case "UNRECOGNIZED_ROOM":
                return MATERIAL_ROOM_UNRECOGNIZED
            case "ROOM_SLAB_FLOOR":
                return this.roomSlabFloorMaterial(component)
            case "ROOM_SLAB_CEILING":
                return MATERIAL_ROOM_SLAB_CEILING
            case "ROOM_SLAB_FLOOR_SEGMENT":
                return MATERIAL_ROOM_SLAB_FLOOR_SEGMENT
            case "ROOM_SLAB_CEILING_SEGMENT":
                return MATERIAL_ROOM_SLAB_CEILING_SEGMENT
            case "ROOM_TEXT_BACKGROUND":
                return MATERIAL_ROOM_TEXT_BACKGROUND
            case "ROOM_TEXT":
                return MATERIAL_ROOM_TEXT
            case "WALL_OPENING_DOOR":
                return MATERIAL_DOOR
            case "WALL_OPENING_DOOR_DOORWAY_ARC":
            case "WALL_OPENING_DOOR_DOORWAY_LINE":
                return MATERIAL_DOORWAY_LINES
            case "WALL_OPENING_WINDOW":
                return MATERIAL_WINDOW
            case "WALL_WITH_HOLES":
            case "WALL_WITHOUT_HOLES":
                return this.wallMaterial(component)
            case "ROOF_AREA":
                return component.component.value.id === RoofAreaCreator.TEMP_ROOF_AREA_ID ? MATERIAL_ROOF_AREA_TEMP : MATERIAL_ROOF_AREA
            case "WALL_METRICS_LINE":
                return this.isEffectedByAnyWallSnapPoint(component.textComponent.wallWithHolesComponent, snapPointComponent =>
                        snapPointComponent.component.value.id !== component.component.value.id && (
                            this.isComponentHovered(snapPointComponent)
                            || this.isComponentSelected(snapPointComponent)
                            || this.isComponentDragged(snapPointComponent)
                        )
                )
                    ? MATERIAL_METRICS_LINE_NOT_FOCUSED
                    : MATERIAL_METRICS_LINE
            case "WALL_OPENING_METRICS_LINE":
                return MATERIAL_METRICS_LINE
            case "WALL_METRICS_TEXT_WIDTH_BACKGROUND":
                return this.isEffectedByAnyWallSnapPoint(component.textComponent.wallWithHolesComponent, snapPointComponent =>
                        snapPointComponent.component.value.id !== component.component.value.id && (
                            this.isComponentHovered(snapPointComponent)
                            || this.isComponentSelected(snapPointComponent)
                            || this.isComponentDragged(snapPointComponent)
                        )
                )
                    ? MATERIAL_METRICS_TEXT_BACKGROUND_NOT_FOCUSED
                    : MATERIAL_METRICS_TEXT_BACKGROUND
            case "WALL_OPENING_METRICS_TEXT_BACKGROUND":
                return MATERIAL_METRICS_TEXT_BACKGROUND
            case "WALL_METRICS_TEXT_DISPLAY_ID":
                return this.isWallDisplayIdEffectedByOtherHoveredOrSelectedWall(component.wallWithHolesComponent)
                    ? MATERIAL_METRICS_TEXT_DISPLAY_ID_NOT_FOCUSED
                    : MATERIAL_METRICS_TEXT_DISPLAY_ID
            case "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND":
                return this.isWallDisplayIdEffectedByOtherHoveredOrSelectedWall(component.textComponent.wallWithHolesComponent)
                    ? MATERIAL_METRICS_TEXT_DISPLAY_ID_BACKGROUND_NOT_FOCUSED
                    : MATERIAL_METRICS_TEXT_DISPLAY_ID_BACKGROUND
            case "WALL_METRICS_TEXT_THICKNESS":
                return MATERIAL_METRICS_TEXT_THICKNESS
            case "WALL_METRICS_TEXT_THICKNESS_BACKGROUND":
                return MATERIAL_METRICS_TEXT_THICKNESS_BACKGROUND
            case "WALL_SIZE_ADJUSTMENT":
                return this.wallMaterial(component)
            case "WALL_METRICS_TEXT_WIDTH":
                return this.isEffectedByAnyWallSnapPoint(component.wallWithHolesComponent, snapPointComponent =>
                        snapPointComponent.component.value.id !== component.component.value.id && (
                            this.isComponentHovered(snapPointComponent)
                            || this.isComponentSelected(snapPointComponent)
                            || this.isComponentDragged(snapPointComponent)
                        )
                )
                    ? MATERIAL_METRICS_TEXT_NOT_FOCUSED
                    : MATERIAL_METRICS_TEXT
            case "WALL_OPENING_METRICS_TEXT":
                return MATERIAL_METRICS_TEXT
            case "POINT_OF_INTEREST":
                return MATERIAL_POINT_OF_INTEREST
            case "POINT_OF_INTEREST_CAMERA":
                return MATERIAL_POINT_OF_INTEREST_CAMERA
            case "FLOOR_GRID":
                return MATERIAL_FLOOR_GRID
            case "WALL_SIZE_ADJUSTMENT_OVERLAY":
                return MATERIAL_WALL_SIZE_ADJUSTMENT
            case "WALL_ROOF_POINT":
                return MATERIAL_WALL_ROOF_POINT
            default:
                return MATERIAL_DEBUG
        }
    }

    calculateHasBillboardEffect(component: BuildingComponent): boolean {
        switch (component.type) {
            case "ROOM_TEXT":
            case "ROOM_TEXT_BACKGROUND":
            case "WALL_METRICS_TEXT_WIDTH":
            case "WALL_METRICS_TEXT_WIDTH_BACKGROUND":
            case "WALL_OPENING_METRICS_TEXT":
            case "WALL_OPENING_METRICS_TEXT_BACKGROUND":
            case "WALL_METRICS_TEXT_DISPLAY_ID":
            case "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND":
            case "WALL_METRICS_TEXT_THICKNESS":
            case "WALL_METRICS_TEXT_THICKNESS_BACKGROUND":
            case "POINT_OF_INTEREST":
                return true
            default:
                return false
        }
    }

    // noinspection OverlyComplexFunctionJS,FunctionTooLongJS
    calculateRenderOrder(component: BuildingComponent): Optional<number> {
        if (component.type === "WALL_OPENING_METRICS_TEXT") {
            return 109
        }
        if (component.type === "WALL_OPENING_METRICS_TEXT_BACKGROUND") {
            return 108
        }
        if (component.type === "WALL_OPENING_METRICS_LINE") {
            return 107
        }
        if (component.type === "WALL_METRICS_TEXT_DISPLAY_ID") {
            return 106
        }
        if (component.type === "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND") {
            return 105
        }
        if (component.type === "WALL_METRICS_TEXT_THICKNESS") {
            return 104
        }
        if (component.type === "WALL_METRICS_TEXT_THICKNESS_BACKGROUND") {
            return 103
        }
        if (component.type === "WALL_METRICS_TEXT_WIDTH") {
            return 102
        }
        if (component.type === "WALL_METRICS_TEXT_WIDTH_BACKGROUND") {
            return 101
        }
        if (component.type === "WALL_METRICS_LINE") {
            return 100
        }

        const isHovered = this.isComponentHovered(component) || (component.type === "WALL_SIZE_ADJUSTMENT" && this.isComponentHovered(component.wallComponent))
        const isSelected = this.isComponentSelected(component) || (component.type === "WALL_SIZE_ADJUSTMENT" && this.isComponentSelected(component.wallComponent))

        if (isHovered || isSelected) {
            return 8
        }

        switch (component.type) {
            case "ROOM_TEXT":
                return 13

            case "ROOM_TEXT_BACKGROUND":
                return 12

            case "POINT_OF_INTEREST_CAMERA":
                return 7

            case "POINT_OF_INTEREST":
                return 6

            case "FURNITURE":
                return 5

            case "WALL_WITH_HOLES":
                return 4

            case "WALL_OPENING_DOOR_DOORWAY_ARC":
            case "WALL_OPENING_DOOR_DOORWAY_LINE":
                return 3

            case "ROOM_SLAB_FLOOR":
                return 2

            case "FLOOR_SLAB_FLOOR":
                return 1

            default:
                return null
        }
    }

    protected calculateZFightingOffsetY(component: BuildingComponent): Optional<number> {
        if (component.type === "ROOM_SLAB_FLOOR") {
            return BUILDING_EPSILON
        }
        if (component.type === "WALL_OPENING_DOOR_DOORWAY_ARC" || component.type === "WALL_OPENING_DOOR_DOORWAY_LINE") {
            return BUILDING_EPSILON * 4
        }
        if (component.type === "ROOF_AREA") {
            return BUILDING_EPSILON
        }
        return null
    }

    // noinspection OverlyComplexFunctionJS
    protected calculateZFightingOffsetZ(component: BuildingComponent): Optional<number> {
        switch (component.type) {
            case "WALL_WITH_HOLES":
            case "WALL_WITHOUT_HOLES":
                return noThicknessWallOffsetZ(this, component.component.value)
            case "WALL_OPENING_DOOR":
            case "WALL_OPENING_WINDOW":
            case "WALL_OPENING_OPENING":
                return noThicknessOpeningOffsetZ(this, component.component.value)
            default:
                return null
        }
    }

    protected calculateZFightingScale(component: BuildingComponent): Optional<Vector3> {
        if (component.type === "WALL_SIZE_ADJUSTMENT_OVERLAY") {
            const factor = 1 + BUILDING_EPSILON
            return new Vector3(
                factor,
                factor,
                factor
            )
        }
        if (component.type === "ROOF_AREA") {
            const factor = 1 + BUILDING_EPSILON
            return new Vector3(
                factor,
                factor,
                factor
            )
        }
        return null
    }

    protected initializeRaycasters(edit: boolean): TRaycaster[] {
        if (edit) {
            return [
                this.occlusionCullingRaycaster,
                this.selectionRaycaster,
                this.hoverRaycaster,
                this.trackingRaycaster!,
            ]
        }
        return [
            this.occlusionCullingRaycaster,
            this.selectionRaycaster,
            this.hoverRaycaster,
        ]
    }

    protected calculateCanBeDeleted(component: RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint): boolean {
        switch (component.type) {
            case "FLOOR":
            case "WALL_WITH_HOLES":
            case "FURNITURE":
            case "WALL_OPENING_OPENING":
            case "WALL_OPENING_DOOR":
            case "WALL_OPENING_WINDOW":
            case "POINT_OF_INTEREST":
            case "ROOF_AREA":
                return true
            default:
                return false
        }
    }

    protected isDraggable(component: BuildingComponent & TDraggableRelated<TDraggableRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent>>>): boolean {
        return false;
    }

    protected calculateShowSelectionDetails(selection: readonly (RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint)[]): boolean {
        return true
    }

    private roomSlabFloorMaterial(component: BuildingComponent & { component: Readonly<Ref<ConstructionPart>> }): TMaterialDeclaration<TMaterial> {
        const floor = this.traversableBuilding.floorOf(component.component.value)
        return floor != null && floorTypeOfFloor(floor) === "CELLAR"
            ? MATERIAL_ROOM_SLAB_FLOOR_BASEMENT
            : MATERIAL_ROOM_SLAB_FLOOR_DEFAULT
    }

    private floorSlabMaterial(component: FloorSlabFloorBuildingComponent | FloorSlabCeilingBuildingComponent): TMaterialDeclaration<TMaterial> {
        if (this.isComponentHovered(component.floorComponent) || this.isComponentSelected(component.floorComponent)) {
            return MATERIAL_SUBSELECTION
        }
        return floorTypeOfFloor(component.floorComponent.component.value) === "CELLAR"
            ? MATERIAL_FLOOR_SLAB_BASEMENT
            : MATERIAL_FLOOR_SLAB_DEFAULT
    }

    // noinspection OverlyComplexFunctionJS
    private wallMaterial(component: WallWithHolesBuildingComponent | WallWithoutHolesBuildingComponent | WallSizeAdjustmentBuildingComponent): TMaterialDeclaration<TMaterial> {
        // if (this.isRoomOfWallHoveredOrSelected(component)) {
        //     return MATERIAL_SELECTION
        // }

        const mode = this.mode.value;
        if (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING" && mode.wall.id === component.component.value.id) {
            return MATERIAL_SELECTION
        }

        const isOcclusionCulled = this.isComponentOcclusionCulled(component)

        const rawComponent = toRaw(component)
        const snapPointIds = "snapPointIds" in rawComponent ? rawComponent.snapPointIds : rawComponent.wallComponent.snapPointIds

        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        this.snappingManager.refreshCounter.value //trigger reactivity
        for (const snapPointId of snapPointIds) {
            if (this.snappingManager.snapPointIdToSnapPointsOfSameGroup(component.component.value, snapPointId, true).length < 2) {
                return isOcclusionCulled ? MATERIAL_WALL_NO_CONNECTION_OCCL_CULLED : MATERIAL_WALL_NO_CONNECTION
            }
        }

        const wall = component.component.value;
        if (wall.isExterior === true) {
            const floor = this.traversableBuilding.floorOf(wall)
            if (floor != null && floorTypeOfFloor(floor) === "CELLAR") {
                return isOcclusionCulled ? MATERIAL_WALL_BASEMENT_OCCL_CULLED : MATERIAL_WALL_BASEMENT
            }
        }

        const wallType = wallTypeOfWall(wall)
        switch (wallType) {
            case "INTERIOR":
                return isOcclusionCulled ? MATERIAL_WALL_INTERIOR_OCCL_CULLED : MATERIAL_WALL_INTERIOR
            case "EXTERIOR":
                return isOcclusionCulled ? MATERIAL_WALL_EXTERIOR_OCCL_CULLED : MATERIAL_WALL_EXTERIOR
            case "INTERMEDIATE":
                return isOcclusionCulled ? MATERIAL_WALL_INTERMEDIATE_OCCL_CULLED : MATERIAL_WALL_INTERMEDIATE
            default:
                console.warn("Unknown wall type", wallType)
                return isOcclusionCulled ? MATERIAL_WALL_INTERIOR_OCCL_CULLED : MATERIAL_WALL_INTERIOR
        }
    }
}