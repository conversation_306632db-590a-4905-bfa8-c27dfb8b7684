<template>
    <d-l-b-r-shape-representation v-for="component in components"
                                  :key="component.id"
                                  :component="component"/>
</template>

<script lang="ts"
        setup>
    import {Wall} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, onUnmounted, toRef} from "vue";
    import {createHoverEmitterAndConsumer} from "@/adapter/three/raycasting/hover/THoverRaycasterEmitterOrConsumer";
    import {calculateWallOrOpeningSnapPointPositionXZ, DBuildingRendererInjection, wallToPolygonEdges, worldTransformationOfWallOrOpening} from "@/components/listing/building/building";
    import {BufferGeometry, Matrix4, SphereGeometry, Vector3} from "three";
    import {CustomGeometryShapeRepresentation, WallRoofPoint, WallRoofPointBuildingComponent, WallWithHolesBuildingComponent, WallWithoutHolesBuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
    import DLBRShapeRepresentation from "@/components/listing/building/renderer/d-l-b-r-shape-representation.vue";
    import {createSelectionEmitterAndConsumer} from "@/adapter/three/raycasting/selection/TSelectionRaycasterEmitterOrConsumer";

    const props = defineProps<{
        wall: Wall
        wallWithHolesComponent: WallWithHolesBuildingComponent
        wallWithoutHolesComponent: WallWithoutHolesBuildingComponent
    }>()

    const propsWall = toRef(() => props.wall)

    const renderer = inject(DBuildingRendererInjection)!

    const roofPointRadius = 0.25 / 2
    const roofPointOffsetY = computed<number>(() => renderer.renderType === "2D" ? 1 : 0)

    const snapPointOffsetZ = computed<number>(() => {
        const wall = propsWall.value
        const snapPointLeft = calculateWallOrOpeningSnapPointPositionXZ(renderer, wall, "LEFT", wall, false)
        const snapPointLeft3D = new Vector3(snapPointLeft.x, 0, snapPointLeft.y)

        const wallWorldTransformation = worldTransformationOfWallOrOpening(renderer, wall)
        const inverseTransformation = wallWorldTransformation.clone().invert()

        const newSnapPointLeft3D = snapPointLeft3D.clone().applyMatrix4(inverseTransformation)
        return newSnapPointLeft3D.z
    })

    const roofPoints = computed<readonly WallRoofPoint[]>(() => {
        const wall = propsWall.value
        const wallWorldTransformation = worldTransformationOfWallOrOpening(renderer, wall)
        const offsetZ = snapPointOffsetZ.value
        const edges = wallToPolygonEdges(propsWall.value).filter(e => e.type === "CENTER" || e.type === "TOP")

        return edges.flatMap((e, index) => {
            const localPositionStart = new Vector3(e.lineSegment.start.x, e.lineSegment.start.y, offsetZ)
            const localPositionEnd = new Vector3(e.lineSegment.end.x, e.lineSegment.end.y, offsetZ)

            const worldPositionStart = localPositionStart.clone().applyMatrix4(wallWorldTransformation)
            const worldPositionEnd = localPositionEnd.clone().applyMatrix4(wallWorldTransformation)

            return [
                {
                    id: `${wall.id}-roofPoint-${index}-start`,
                    type: "WALL_ROOF_POINT",
                    localPosition: localPositionStart,
                    worldPosition: worldPositionStart,
                    wall
                } satisfies WallRoofPoint,
                {
                    id: `${wall.id}-roofPoint-${index}-end`,
                    type: "WALL_ROOF_POINT",
                    localPosition: localPositionEnd,
                    worldPosition: worldPositionEnd,
                    wall
                } satisfies WallRoofPoint,
            ]
        })
    })

    function createShapeRepresentation(roofPoint: WallRoofPoint): CustomGeometryShapeRepresentation {
        return {
            transformationMatrix: computed<Matrix4>(() => new Matrix4().makeTranslation(
                roofPoint.localPosition.x,
                roofPoint.localPosition.y + roofPointOffsetY.value,
                roofPoint.localPosition.z
            )),
            customShapeType: "GEOMETRY",
            geometry: computed<BufferGeometry>(oldGeometry => {
                oldGeometry?.dispose()

                return new SphereGeometry(roofPointRadius, 32, 32)
            }),
            addToScene: false,
        }
    }

    const components = computed<readonly WallRoofPointBuildingComponent[]>(() => roofPoints.value.map(roofPoint => ({
        id: roofPoint.id,
        type: "WALL_ROOF_POINT",
        component: propsWall,
        wallWithHolesComponent: props.wallWithHolesComponent,
        wallWithoutHolesComponent: props.wallWithoutHolesComponent,
        roofPoint,
        customShapeRepresentation: createShapeRepresentation(roofPoint),
        selection: createSelectionEmitterAndConsumer(roofPoint.id, roofPoint),
        hover: createHoverEmitterAndConsumer(roofPoint.id, roofPoint),
    })))

    onUnmounted(() => {
        components.value.forEach(({customShapeRepresentation}) => customShapeRepresentation.geometry.value.dispose())
    })
</script>

<style scoped>
</style>